using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// Exemplo de sistema de diálogo usando o Visual Text Displayer com o editor melhorado.
/// Demonstra como usar as novas funcionalidades de controle externo em um contexto prático.
/// </summary>
public class DialogueSystemExample : MonoBehaviour
{
    [Header("Dialogue System Components")]
    [Tooltip("O Visual Text Displayer para exibir o diálogo.")]
    public VisualTextDisplayer dialogueDisplayer;
    
    [Tooltip("Painel UI do sistema de diálogo.")]
    public GameObject dialoguePanel;
    
    [Tooltip("Nome do personagem falando.")]
    public Text characterNameText;
    
    [Header("Dialogue Content")]
    [Tooltip("Lista de documentos de diálogo para este exemplo.")]
    public List<VisualTextDocument> dialogueDocuments = new List<VisualTextDocument>();
    
    [Tooltip("Lista de nomes de personagens correspondentes aos documentos.")]
    public List<string> characterNames = new List<string>();
    
    [Header("Control Buttons")]
    [Tooltip("Botão para avançar o diálogo.")]
    public Button nextButton;
    
    [Tooltip("Botão para voltar no diálogo.")]
    public Button previousButton;
    
    [Tooltip("Botão para pular a animação atual.")]
    public Button skipButton;
    
    [Tooltip("Botão para fechar o diálogo.")]
    public Button closeButton;
    
    [Header("Settings")]
    [Tooltip("Se deve iniciar o diálogo automaticamente.")]
    public bool autoStartDialogue = false;
    
    [Tooltip("Se deve fechar automaticamente ao terminar.")]
    public bool autoCloseOnEnd = true;
    
    // Estado interno
    private int currentDialogueIndex = 0;
    private bool isDialogueActive = false;

    void Start()
    {
        SetupDialogueSystem();
        
        if (autoStartDialogue && dialogueDocuments.Count > 0)
        {
            StartDialogue();
        }
    }

    void SetupDialogueSystem()
    {
        // Configura os botões
        if (nextButton != null)
            nextButton.onClick.AddListener(NextDialogue);
            
        if (previousButton != null)
            previousButton.onClick.AddListener(PreviousDialogue);
            
        if (skipButton != null)
            skipButton.onClick.AddListener(SkipCurrentAnimation);
            
        if (closeButton != null)
            closeButton.onClick.AddListener(CloseDialogue);
        
        // Configura eventos do displayer
        if (dialogueDisplayer != null)
        {
            dialogueDisplayer.OnDisplayFinished.AddListener(OnDialogueFinished);
        }
        
        // Inicialmente esconde o painel
        if (dialoguePanel != null)
            dialoguePanel.SetActive(false);
            
        UpdateButtonStates();
    }

    /// <summary>
    /// Inicia o sistema de diálogo.
    /// </summary>
    public void StartDialogue()
    {
        if (dialogueDocuments.Count == 0)
        {
            Debug.LogWarning("No dialogue documents assigned!");
            return;
        }

        currentDialogueIndex = 0;
        isDialogueActive = true;
        
        if (dialoguePanel != null)
            dialoguePanel.SetActive(true);
            
        DisplayCurrentDialogue();
    }

    /// <summary>
    /// Avança para o próximo diálogo.
    /// </summary>
    public void NextDialogue()
    {
        if (!isDialogueActive) return;

        if (dialogueDisplayer.IsDisplaying)
        {
            // Se ainda está exibindo, usa a navegação por sentença
            dialogueDisplayer.NextSentence();
        }
        else
        {
            // Se terminou, vai para o próximo documento
            if (currentDialogueIndex < dialogueDocuments.Count - 1)
            {
                currentDialogueIndex++;
                DisplayCurrentDialogue();
            }
            else if (autoCloseOnEnd)
            {
                CloseDialogue();
            }
        }
        
        UpdateButtonStates();
    }

    /// <summary>
    /// Volta para o diálogo anterior.
    /// </summary>
    public void PreviousDialogue()
    {
        if (!isDialogueActive) return;

        if (dialogueDisplayer.IsDisplaying)
        {
            // Se ainda está exibindo, usa a navegação por sentença
            dialogueDisplayer.PreviousSentence();
        }
        else
        {
            // Se terminou, volta para o documento anterior
            if (currentDialogueIndex > 0)
            {
                currentDialogueIndex--;
                DisplayCurrentDialogue();
            }
        }
        
        UpdateButtonStates();
    }

    /// <summary>
    /// Pula a animação atual.
    /// </summary>
    public void SkipCurrentAnimation()
    {
        if (!isDialogueActive || dialogueDisplayer == null) return;
        
        dialogueDisplayer.Skip();
    }

    /// <summary>
    /// Fecha o sistema de diálogo.
    /// </summary>
    public void CloseDialogue()
    {
        isDialogueActive = false;
        
        if (dialogueDisplayer != null)
            dialogueDisplayer.Stop();
            
        if (dialoguePanel != null)
            dialoguePanel.SetActive(false);
            
        UpdateButtonStates();
    }

    /// <summary>
    /// Exibe o diálogo atual baseado no índice.
    /// </summary>
    private void DisplayCurrentDialogue()
    {
        if (currentDialogueIndex < 0 || currentDialogueIndex >= dialogueDocuments.Count)
            return;

        var currentDocument = dialogueDocuments[currentDialogueIndex];
        
        // Atualiza o nome do personagem
        if (characterNameText != null && currentDialogueIndex < characterNames.Count)
        {
            characterNameText.text = characterNames[currentDialogueIndex];
        }
        
        // Exibe o documento
        if (dialogueDisplayer != null)
        {
            dialogueDisplayer.Display(currentDocument);
        }
        
        UpdateButtonStates();
    }

    /// <summary>
    /// Atualiza o estado dos botões baseado na situação atual.
    /// </summary>
    private void UpdateButtonStates()
    {
        if (nextButton != null)
        {
            nextButton.interactable = isDialogueActive && 
                (dialogueDisplayer.IsDisplaying || currentDialogueIndex < dialogueDocuments.Count - 1);
        }
        
        if (previousButton != null)
        {
            previousButton.interactable = isDialogueActive && 
                (dialogueDisplayer.IsDisplaying || currentDialogueIndex > 0);
        }
        
        if (skipButton != null)
        {
            skipButton.interactable = isDialogueActive && dialogueDisplayer.IsDisplaying;
        }
        
        if (closeButton != null)
        {
            closeButton.interactable = isDialogueActive;
        }
    }

    /// <summary>
    /// Chamado quando um diálogo termina de ser exibido.
    /// </summary>
    private void OnDialogueFinished()
    {
        UpdateButtonStates();
        
        // Aqui você pode adicionar lógica adicional, como:
        // - Tocar sons de conclusão
        // - Mostrar indicadores visuais
        // - Ativar escolhas do jogador
        // - etc.
    }

    void Update()
    {
        if (!isDialogueActive) return;

        // Controles por teclado
        if (Input.GetKeyDown(KeyCode.Space) || Input.GetKeyDown(KeyCode.Return))
        {
            NextDialogue();
        }
        
        if (Input.GetKeyDown(KeyCode.Backspace))
        {
            PreviousDialogue();
        }
        
        if (Input.GetKeyDown(KeyCode.LeftControl) || Input.GetKeyDown(KeyCode.RightControl))
        {
            SkipCurrentAnimation();
        }
        
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            CloseDialogue();
        }
        
        // Atualiza estados dos botões continuamente durante runtime
        UpdateButtonStates();
    }

    /// <summary>
    /// Método público para iniciar um diálogo específico.
    /// </summary>
    public void StartSpecificDialogue(int dialogueIndex)
    {
        if (dialogueIndex >= 0 && dialogueIndex < dialogueDocuments.Count)
        {
            currentDialogueIndex = dialogueIndex;
            StartDialogue();
        }
    }

    /// <summary>
    /// Adiciona um novo documento de diálogo em runtime.
    /// </summary>
    public void AddDialogue(VisualTextDocument document, string characterName = "")
    {
        if (document != null)
        {
            dialogueDocuments.Add(document);
            characterNames.Add(characterName);
        }
    }

    void OnValidate()
    {
        // Sincroniza o tamanho das listas no editor
        while (characterNames.Count < dialogueDocuments.Count)
        {
            characterNames.Add("Character " + (characterNames.Count + 1));
        }
        
        while (characterNames.Count > dialogueDocuments.Count)
        {
            characterNames.RemoveAt(characterNames.Count - 1);
        }
    }
}
