using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Exemplo de como usar as funções de controle externo do Visual Text Displayer.
/// Este script demonstra como navegar entre frases/sentenças e controlar a reprodução.
/// </summary>
public class VisualTextDisplayerControlExample : MonoBehaviour
{
    [Header("Referências")]
    [Tooltip("O Visual Text Displayer a ser controlado.")]
    public VisualTextDisplayer textDisplayer;
    
    [Tooltip("Documento de texto para exibir.")]
    public VisualTextDocument testDocument;
    
    [Header("Botões de Controle (Opcional)")]
    [Tooltip("Botão para ir para a frase anterior.")]
    public Button previousPhraseButton;
    
    [Tooltip("Botão para ir para a próxima frase.")]
    public Button nextPhraseButton;
    
    [Tooltip("Botão para ir para a sentença anterior.")]
    public Button previousSentenceButton;
    
    [<PERSON>lt<PERSON>("Botão para ir para a próxima sentença.")]
    public Button nextSentenceButton;
    
    [Tooltip("Botão para reproduzir/pausar.")]
    public Button playPauseButton;
    
    [Tooltip("Botão para parar.")]
    public Button stopButton;
    
    [Tooltip("Botão para limpar.")]
    public Button clearButton;
    
    [Header("Informações de Estado")]
    [Tooltip("Texto para mostrar o estado atual.")]
    public Text statusText;

    void Start()
    {
        // Configura os botões se estiverem atribuídos
        SetupButtons();
        
        // Carrega o documento de teste se estiver atribuído
        if (testDocument != null && textDisplayer != null)
        {
            textDisplayer.Display(testDocument);
        }
    }

    void Update()
    {
        // Atualiza o texto de status
        UpdateStatusText();
        
        // Controles por teclado para teste
        HandleKeyboardInput();
    }

    void SetupButtons()
    {
        if (previousPhraseButton != null)
            previousPhraseButton.onClick.AddListener(() => textDisplayer?.PreviousPhrase());
            
        if (nextPhraseButton != null)
            nextPhraseButton.onClick.AddListener(() => textDisplayer?.NextPhrase());
            
        if (previousSentenceButton != null)
            previousSentenceButton.onClick.AddListener(() => textDisplayer?.PreviousSentence());
            
        if (nextSentenceButton != null)
            nextSentenceButton.onClick.AddListener(() => textDisplayer?.NextSentence());
            
        if (playPauseButton != null)
            playPauseButton.onClick.AddListener(TogglePlayPause);
            
        if (stopButton != null)
            stopButton.onClick.AddListener(() => textDisplayer?.Stop());
            
        if (clearButton != null)
            clearButton.onClick.AddListener(() => textDisplayer?.Clear());
    }

    void HandleKeyboardInput()
    {
        if (textDisplayer == null) return;

        // Navegação por frases
        if (Input.GetKeyDown(KeyCode.LeftArrow))
        {
            textDisplayer.PreviousPhrase();
        }
        else if (Input.GetKeyDown(KeyCode.RightArrow))
        {
            textDisplayer.NextPhrase();
        }
        
        // Navegação por sentenças
        if (Input.GetKeyDown(KeyCode.UpArrow))
        {
            textDisplayer.PreviousSentence();
        }
        else if (Input.GetKeyDown(KeyCode.DownArrow))
        {
            textDisplayer.NextSentence();
        }
        
        // Controles de reprodução
        if (Input.GetKeyDown(KeyCode.Space))
        {
            TogglePlayPause();
        }
        else if (Input.GetKeyDown(KeyCode.S))
        {
            textDisplayer.Stop();
        }
        else if (Input.GetKeyDown(KeyCode.C))
        {
            textDisplayer.Clear();
        }
    }

    void TogglePlayPause()
    {
        if (textDisplayer == null) return;

        if (textDisplayer.IsPlaying())
        {
            textDisplayer.Pause();
        }
        else
        {
            textDisplayer.Play();
        }
    }

    void UpdateStatusText()
    {
        if (statusText == null || textDisplayer == null) return;

        var currentDoc = textDisplayer.GetCurrentDocument();
        if (currentDoc == null)
        {
            statusText.text = "Nenhum documento carregado";
            return;
        }

        string status = $"Documento: {currentDoc.name}\n";
        status += $"Frase: {textDisplayer.GetCurrentPhraseIndex() + 1}/{currentDoc.Phrases.Count}\n";
        
        if (textDisplayer.GetCurrentPhraseIndex() >= 0 && 
            textDisplayer.GetCurrentPhraseIndex() < currentDoc.Phrases.Count)
        {
            var currentPhrase = currentDoc.Phrases[textDisplayer.GetCurrentPhraseIndex()];
            status += $"Sentença: {textDisplayer.GetCurrentSentenceIndex() + 1}/{currentPhrase.Sentences.Count}\n";
        }
        
        status += $"Estado: ";
        if (textDisplayer.IsPlaying())
        {
            status += "Reproduzindo";
        }
        else if (textDisplayer.IsPaused())
        {
            status += "Pausado";
        }
        else
        {
            status += "Parado";
        }
        
        if (textDisplayer.IsFastForwardActive())
        {
            status += " (Fast Forward)";
        }

        statusText.text = status;
    }

    /// <summary>
    /// Método público para carregar um novo documento.
    /// </summary>
    public void LoadDocument(VisualTextDocument document)
    {
        if (textDisplayer != null && document != null)
        {
            textDisplayer.Display(document);
        }
    }

    /// <summary>
    /// Método público para exibir texto simples.
    /// </summary>
    public void DisplaySimpleText(string text)
    {
        if (textDisplayer != null && !string.IsNullOrEmpty(text))
        {
            textDisplayer.Display(text);
        }
    }
}
