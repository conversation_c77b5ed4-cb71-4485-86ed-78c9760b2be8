# Visual Text Displayer - External Control Functions

## Overview

The Visual Text Displayer now includes external control functions that allow other scripts to easily interact with and control text playback. These functions provide navigation capabilities and playback control while maintaining compatibility with the existing text animation system, event handling, and state management.

## New State Tracking

The Visual Text Displayer now tracks the following state information:

- **Current Document**: The currently loaded VisualTextDocument
- **Current Phrase Index**: Index of the currently displayed phrase
- **Current Sentence Index**: Index of the currently displayed sentence within the current phrase
- **Playing State**: Whether the displayer is currently playing text
- **Paused State**: Whether the displayer is paused

## Navigation Controls

### Phrase-Level Navigation

#### `PreviousPhrase()`
- Navigates to the previous phrase in the current document
- Resets sentence index to 0 (first sentence of the phrase)
- Displays the entire phrase immediately
- Logs warning if already at the first phrase

#### `NextPhrase()`
- Navigates to the next phrase in the current document
- Resets sentence index to 0 (first sentence of the phrase)
- Displays the entire phrase immediately
- Logs warning if already at the last phrase

### Sentence-Level Navigation

#### `PreviousSentence()`
- Navigates to the previous sentence in the current phrase
- If at the first sentence of a phrase, moves to the last sentence of the previous phrase
- Displays only the target sentence
- Logs warning if already at the first sentence of the first phrase

#### `NextSentence()`
- Navigates to the next sentence in the current phrase
- If at the last sentence of a phrase, moves to the first sentence of the next phrase
- Displays only the target sentence
- Logs warning if already at the last sentence of the last phrase

## Playback Controls

### `Play()`
- Starts or resumes text animation/display
- If paused, resumes from current position
- If stopped, starts playing the current document from the beginning
- Logs warning if no document is loaded

### `Stop()`
- Stops current text animation/display completely
- Resets playing and paused states
- Calls the existing `StopDisplay()` method

### `Pause()`
- Pauses the current text animation/display
- Sets the paused state to true
- Note: Full pause implementation would require deeper coroutine modifications

### `Clear()`
- Clears all displayed text
- Stops any running animations
- Resets text component content

## State Query Methods

### `IsPlaying()`
- Returns true if the displayer is currently playing text

### `IsPaused()`
- Returns true if the displayer is paused

### `GetCurrentPhraseIndex()`
- Returns the index of the current phrase (0-based)

### `GetCurrentSentenceIndex()`
- Returns the index of the current sentence within the current phrase (0-based)

### `GetCurrentDocument()`
- Returns the currently loaded VisualTextDocument

## Integration with Existing Systems

### Automatic State Tracking
- The `DisplayDocumentCoroutine` now automatically updates phrase and sentence indices as it progresses
- State is only tracked for main documents (not temporary documents created for navigation)
- Temporary documents are identified by names starting with "TempDocument_"

### Compatibility
- All existing functionality remains unchanged
- Existing events (OnPhraseStarted, OnSentenceStarted, etc.) continue to work
- Fast forward, skip, and other existing controls work alongside new navigation
- Typewriter effects, deletion animations, and replacement systems are preserved

### Material and Effect Preservation
- Navigation functions create temporary documents that preserve original document settings
- Font assets, colors, sizes, and other visual properties are maintained
- Text effects and animations work correctly with navigated content

## Usage Examples

### Basic Navigation
```csharp
// Navigate between phrases
textDisplayer.NextPhrase();
textDisplayer.PreviousPhrase();

// Navigate between sentences
textDisplayer.NextSentence();
textDisplayer.PreviousSentence();
```

### Playback Control
```csharp
// Start/resume playback
textDisplayer.Play();

// Pause playback
textDisplayer.Pause();

// Stop playback
textDisplayer.Stop();

// Clear display
textDisplayer.Clear();
```

### State Queries
```csharp
// Check current state
bool isPlaying = textDisplayer.IsPlaying();
bool isPaused = textDisplayer.IsPaused();

// Get current position
int phraseIndex = textDisplayer.GetCurrentPhraseIndex();
int sentenceIndex = textDisplayer.GetCurrentSentenceIndex();
VisualTextDocument currentDoc = textDisplayer.GetCurrentDocument();
```

## Implementation Notes

### Temporary Documents
- Navigation functions create temporary VisualTextDocument instances for displaying individual phrases or sentences
- These temporary documents inherit all settings from the original document
- Temporary documents are automatically cleaned up after use

### Performance Considerations
- Navigation creates new temporary documents but reuses existing sentence data
- State tracking adds minimal overhead to the existing display process
- Temporary document creation is optimized for quick navigation

### Thread Safety
- All methods are designed to be called from the main Unity thread
- State changes are synchronized with the existing coroutine system

## Example Implementation

See `VisualTextDisplayerControlExample.cs` for a complete example showing:
- Keyboard controls for navigation (arrow keys)
- UI button integration
- Status display showing current position and state
- Play/pause toggle functionality

This example demonstrates how to integrate the external control functions into a user interface or input system.
