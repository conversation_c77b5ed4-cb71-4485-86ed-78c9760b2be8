using UnityEngine;
using UnityEditor;
using TMPro;

/// <summary>
/// Editor customizado para o Visual Text Displayer que organiza as propriedades em abas
/// e fornece controles de teste e informações de estado.
/// </summary>
[CustomEditor(typeof(VisualTextDisplayer), true)]
public class VisualTextDisplayerEditor : Editor
{
    private int _tabIndex = 0;
    private readonly string[] _tabNames = { "📝 Setup", "⚙️ Settings", "🎮 Controls", "📊 Status" };
    
    // Cores para UI
    private readonly Color _headerColor = new Color(0.2f, 0.4f, 0.8f, 0.3f);
    private readonly Color _successColor = new Color(0.2f, 0.8f, 0.2f, 0.3f);
    private readonly Color _warningColor = new Color(0.8f, 0.6f, 0.2f, 0.3f);
    private readonly Color _errorColor = new Color(0.8f, 0.2f, 0.2f, 0.3f);
    private readonly Color _tabBackgroundColor = new Color(0.9f, 0.9f, 0.9f, 0.1f);
    
    // Estado para teste
    private string _testText = "Hello, World! This is a test message.";
    private VisualTextDocument _testDocument;

    // Controles avançados
    private int _targetPhraseIndex = 0;
    private int _targetSentenceIndex = 0;
    private bool _autoRefreshStatus = true;
    private double _lastRefreshTime = 0;

    // Foldouts
    private bool _showEvents = false;
    private bool _showDefaultSettings = true;
    private bool _showFastForwardSettings = true;
    private bool _showAdvancedNavigation = false;
    private bool _showDocumentInfo = true;
    private bool _showPlaybackControls = true;
    
    public override void OnInspectorGUI()
    {
        VisualTextDisplayer displayer = (VisualTextDisplayer)target;
        serializedObject.Update();
        
        DrawHeader(displayer);
        DrawTabs();
        
        EditorGUILayout.Space(5);
        
        switch (_tabIndex)
        {
            case 0: DrawSetupTab(); break;
            case 1: DrawSettingsTab(); break;
            case 2: DrawControlsTab(displayer); break;
            case 3: DrawStatusTab(displayer); break;
        }
        
        if (GUI.changed)
        {
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(displayer);
        }
    }
    
    private void DrawHeader(VisualTextDisplayer displayer)
    {
        // Header com fundo colorido
        Rect headerRect = EditorGUILayout.GetControlRect(false, 60);
        EditorGUI.DrawRect(headerRect, _headerColor);
        
        GUILayout.BeginArea(headerRect);
        GUILayout.Space(8);
        
        GUILayout.BeginHorizontal();
        GUILayout.Space(10);
        
        // Ícone e título
        GUILayout.BeginVertical();
        GUILayout.Label("🎭 Visual Text Displayer", EditorStyles.largeLabel);
        
        // Status rápido
        string status = "Idle";
        Color statusColor = Color.gray;
        
        if (Application.isPlaying && displayer != null)
        {
            if (displayer.IsDisplaying)
            {
                status = displayer.IsPlaying() ? "Playing" : (displayer.IsPaused() ? "Paused" : "Displaying");
                statusColor = displayer.IsPlaying() ? Color.green : (displayer.IsPaused() ? Color.yellow : Color.cyan);
            }
        }
        
        var oldColor = GUI.color;
        GUI.color = statusColor;
        GUILayout.Label($"Status: {status}", EditorStyles.miniLabel);
        GUI.color = oldColor;
        
        GUILayout.EndVertical();
        
        GUILayout.FlexibleSpace();
        
        // Validação rápida
        if (displayer.textComponent == null)
        {
            GUI.color = Color.red;
            GUILayout.Label("⚠️ No TextMeshPro", EditorStyles.boldLabel);
            GUI.color = oldColor;
        }
        else
        {
            GUI.color = Color.green;
            GUILayout.Label("✅ Ready", EditorStyles.boldLabel);
            GUI.color = oldColor;
        }
        
        GUILayout.Space(10);
        GUILayout.EndHorizontal();
        
        GUILayout.EndArea();
    }
    
    private void DrawTabs()
    {
        // Fundo das abas
        Rect tabRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(tabRect, _tabBackgroundColor);
        
        _tabIndex = GUI.Toolbar(tabRect, _tabIndex, _tabNames);
    }
    
    private void DrawSetupTab()
    {
        DrawSectionHeader("🔧 Component References");
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("textComponent"), 
            new GUIContent("Text Component", "O componente TextMeshProUGUI onde o texto será exibido"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("audioSource"), 
            new GUIContent("Audio Source", "AudioSource para efeitos sonoros de texto"));
        
        EditorGUILayout.Space(10);
        
        // Validação e auto-setup
        VisualTextDisplayer displayer = (VisualTextDisplayer)target;
        
        if (displayer.textComponent == null)
        {
            EditorGUILayout.HelpBox("TextMeshProUGUI component is required!", MessageType.Error);
            
            if (GUILayout.Button("🔍 Auto-Find TextMeshPro Component"))
            {
                var textComp = displayer.GetComponent<TextMeshProUGUI>();
                if (textComp != null)
                {
                    serializedObject.FindProperty("textComponent").objectReferenceValue = textComp;
                    serializedObject.ApplyModifiedProperties();
                }
                else
                {
                    EditorUtility.DisplayDialog("Not Found", 
                        "No TextMeshProUGUI component found on this GameObject. Please add one first.", "OK");
                }
            }
        }
        
        if (displayer.audioSource == null)
        {
            EditorGUILayout.HelpBox("AudioSource is optional but recommended for typewriter sounds.", MessageType.Info);
            
            if (GUILayout.Button("🔍 Auto-Find AudioSource Component"))
            {
                var audioComp = displayer.GetComponent<AudioSource>();
                if (audioComp != null)
                {
                    serializedObject.FindProperty("audioSource").objectReferenceValue = audioComp;
                    serializedObject.ApplyModifiedProperties();
                }
                else
                {
                    if (EditorUtility.DisplayDialog("Add AudioSource?", 
                        "No AudioSource found. Would you like to add one?", "Yes", "No"))
                    {
                        var newAudioSource = displayer.gameObject.AddComponent<AudioSource>();
                        newAudioSource.playOnAwake = false;
                        serializedObject.FindProperty("audioSource").objectReferenceValue = newAudioSource;
                        serializedObject.ApplyModifiedProperties();
                    }
                }
            }
        }
        
        EditorGUILayout.Space(10);
        
        // Events section
        DrawSectionHeader("📡 Events");
        _showEvents = EditorGUILayout.Foldout(_showEvents, "Event Callbacks", true);
        
        if (_showEvents)
        {
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnDisplayStarted"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnDisplayFinished"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnPhraseStarted"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnPhraseFinished"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnSentenceStarted"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnSentenceFinished"));
            EditorGUI.indentLevel--;
        }
    }
    
    private void DrawSettingsTab()
    {
        // Fast Forward Settings
        DrawSectionHeader("⚡ Fast Forward Settings");
        _showFastForwardSettings = EditorGUILayout.Foldout(_showFastForwardSettings, "Fast Forward Configuration", true);
        
        if (_showFastForwardSettings)
        {
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(serializedObject.FindProperty("fastForwardTypewriterMultiplier"), 
                new GUIContent("Typewriter Multiplier", "Multiplicador de velocidade para o typewriter durante fast forward"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("fastForwardAnimationMultiplier"), 
                new GUIContent("Animation Multiplier", "Multiplicador de velocidade para animações durante fast forward"));
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space(10);
        
        // Default Settings for Simple Text
        DrawSectionHeader("📝 Default Settings for Simple Text");
        _showDefaultSettings = EditorGUILayout.Foldout(_showDefaultSettings, "Simple Text Configuration", true);
        
        if (_showDefaultSettings)
        {
            EditorGUI.indentLevel++;
            
            // Visual settings
            EditorGUILayout.LabelField("Visual Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultTextColor"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultFontAsset"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultFontSize"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultIsBold"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultIsItalic"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultAlignment"));
            
            EditorGUILayout.Space(5);
            
            // Timing settings
            EditorGUILayout.LabelField("Timing Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultDelayAfterSentence"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultDelayAfterPhrase"));
            
            EditorGUILayout.Space(5);
            
            // Typewriter settings
            EditorGUILayout.LabelField("Typewriter Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultUseTypewriter"));
            
            if (serializedObject.FindProperty("defaultUseTypewriter").boolValue)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultDelayPerCharacter"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultPauseOnPunctuation"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultPunctuationDelay"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultTypewriterSound"));
                EditorGUI.indentLevel--;
            }
            
            EditorGUI.indentLevel--;
        }
    }

    private void DrawControlsTab(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("🎮 Test Controls");

        EditorGUILayout.HelpBox("Use these controls to test the Visual Text Displayer functionality in the editor.", MessageType.Info);

        EditorGUILayout.Space(5);

        // Test Text Input
        EditorGUILayout.LabelField("Simple Text Test", EditorStyles.boldLabel);
        _testText = EditorGUILayout.TextArea(_testText, GUILayout.Height(60));

        EditorGUILayout.BeginHorizontal();

        GUI.enabled = !string.IsNullOrEmpty(_testText) && displayer.textComponent != null;
        if (GUILayout.Button("▶️ Display Simple Text"))
        {
            if (Application.isPlaying)
            {
                displayer.Display(_testText);
            }
            else
            {
                displayer.DisplayFromEditor(_testText);
            }
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(10);

        // Document Test
        EditorGUILayout.LabelField("Document Test", EditorStyles.boldLabel);
        _testDocument = (VisualTextDocument)EditorGUILayout.ObjectField("Test Document", _testDocument, typeof(VisualTextDocument), false);

        EditorGUILayout.BeginHorizontal();

        GUI.enabled = _testDocument != null && displayer.textComponent != null;
        if (GUILayout.Button("▶️ Display Document"))
        {
            if (Application.isPlaying)
            {
                displayer.Display(_testDocument);
            }
            else
            {
                displayer.DisplayFromEditor(_testDocument);
            }
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(10);

        // Runtime Controls
        if (Application.isPlaying)
        {
            DrawRuntimeControls(displayer);
        }
        else
        {
            EditorGUILayout.HelpBox("Advanced runtime controls are only available during play mode.", MessageType.Info);
        }

        EditorGUILayout.Space(10);

        // Clear Controls
        DrawSectionHeader("🧹 Clear Controls");

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("🗑️ Clear Text"))
        {
            if (displayer.textComponent != null)
            {
                displayer.textComponent.text = "";
            }
        }

        if (GUILayout.Button("⏹️ Stop All"))
        {
            displayer.StopDisplay();
        }

        EditorGUILayout.EndHorizontal();
    }

    private void DrawRuntimeControls(VisualTextDisplayer displayer)
    {
        // Current Document Info
        DrawCurrentDocumentInfo(displayer);

        EditorGUILayout.Space(10);

        // Playback Controls
        DrawPlaybackControls(displayer);

        EditorGUILayout.Space(10);

        // Navigation Controls
        DrawNavigationControls(displayer);

        EditorGUILayout.Space(10);

        // Advanced Navigation
        DrawAdvancedNavigation(displayer);
    }

    private void DrawCurrentDocumentInfo(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("📄 Current Document");

        var currentDoc = displayer._currentDocument;
        if (currentDoc != null)
        {
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ObjectField("Loaded Document", currentDoc, typeof(VisualTextDocument), false);
            EditorGUILayout.LabelField("Total Phrases", currentDoc.Phrases?.Count.ToString() ?? "0");
            EditorGUI.EndDisabledGroup();

            // Current position
            int currentPhraseIndex = displayer.GetCurrentPhraseIndex();
            int currentSentenceIndex = displayer.GetCurrentSentenceIndex();

            EditorGUILayout.LabelField("Current Position", $"Phrase {currentPhraseIndex + 1}/{currentDoc.Phrases?.Count ?? 0}, Sentence {currentSentenceIndex + 1}");

            if (currentPhraseIndex >= 0 && currentPhraseIndex < currentDoc.Phrases.Count)
            {
                var currentPhrase = currentDoc.Phrases[currentPhraseIndex];
                EditorGUILayout.LabelField("Sentences in Phrase", currentPhrase.Sentences?.Count.ToString() ?? "0");

                if (currentSentenceIndex >= 0 && currentSentenceIndex < currentPhrase.Sentences.Count)
                {
                    var currentSentence = currentPhrase.Sentences[currentSentenceIndex];
                    EditorGUILayout.LabelField("Current Text", currentSentence.Text ?? "");
                }
            }
        }
        else
        {
            EditorGUILayout.HelpBox("No document currently loaded.", MessageType.Info);

            // Allow loading a document
            _testDocument = (VisualTextDocument)EditorGUILayout.ObjectField("Load Document", _testDocument, typeof(VisualTextDocument), false);

            GUI.enabled = _testDocument != null;
            if (GUILayout.Button("📂 Load Selected Document"))
            {
                displayer.Display(_testDocument);
            }
            GUI.enabled = true;
        }
    }

    private void DrawPlaybackControls(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("🎛️ Playback Controls");

        // State display
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("State:", GUILayout.Width(50));

        string state = "Idle";
        Color stateColor = Color.gray;

        if (displayer.IsDisplaying)
        {
            if (displayer.IsPlaying())
            {
                state = "Playing";
                stateColor = Color.green;
            }
            else if (displayer.IsPaused())
            {
                state = "Paused";
                stateColor = Color.yellow;
            }
            else
            {
                state = "Displaying";
                stateColor = Color.cyan;
            }
        }

        var oldColor = GUI.color;
        GUI.color = stateColor;
        EditorGUILayout.LabelField(state, EditorStyles.boldLabel);
        GUI.color = oldColor;

        if (displayer.IsFastForwardActive())
        {
            GUI.color = Color.magenta;
            EditorGUILayout.LabelField("⚡ Fast Forward", EditorStyles.boldLabel);
            GUI.color = oldColor;
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(5);

        // Main playback controls
        EditorGUILayout.BeginHorizontal();

        if (displayer.IsPlaying())
        {
            if (GUILayout.Button("⏸️ Pause"))
            {
                displayer.Pause();
            }
        }
        else
        {
            if (GUILayout.Button("▶️ Play"))
            {
                displayer.Play();
            }
        }

        if (GUILayout.Button("⏹️ Stop"))
        {
            displayer.Stop();
        }

        if (GUILayout.Button("🗑️ Clear"))
        {
            displayer.Clear();
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(5);

        // Additional controls
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("⏭️ Skip"))
        {
            displayer.Skip();
        }

        string fastForwardText = displayer.IsFastForwardActive() ? "🐌 Normal Speed" : "⚡ Fast Forward";
        if (GUILayout.Button(fastForwardText))
        {
            displayer.ToggleFastForward();
        }

        EditorGUILayout.EndHorizontal();
    }

    private void DrawNavigationControls(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("🧭 Navigation Controls");

        // Phrase navigation
        EditorGUILayout.LabelField("Phrase Navigation", EditorStyles.boldLabel);
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("⏮️ Previous Phrase"))
        {
            displayer.PreviousPhrase();
        }

        if (GUILayout.Button("⏭️ Next Phrase"))
        {
            displayer.NextPhrase();
        }

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(5);

        // Sentence navigation
        EditorGUILayout.LabelField("Sentence Navigation", EditorStyles.boldLabel);
        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("⏪ Previous Sentence"))
        {
            displayer.PreviousSentence();
        }

        if (GUILayout.Button("⏩ Next Sentence"))
        {
            displayer.NextSentence();
        }

        EditorGUILayout.EndHorizontal();
    }

    private void DrawAdvancedNavigation(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("🎯 Advanced Navigation");

        _showAdvancedNavigation = EditorGUILayout.Foldout(_showAdvancedNavigation, "Jump to Specific Position", true);

        if (_showAdvancedNavigation)
        {
            EditorGUI.indentLevel++;

            var currentDoc = displayer._currentDocument;
            if (currentDoc != null && currentDoc.Phrases != null)
            {
                // Phrase jump
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Jump to Phrase:", GUILayout.Width(100));
                _targetPhraseIndex = EditorGUILayout.IntSlider(_targetPhraseIndex, 0, currentDoc.Phrases.Count - 1);

                GUI.enabled = _targetPhraseIndex >= 0 && _targetPhraseIndex < currentDoc.Phrases.Count;
                if (GUILayout.Button("Go", GUILayout.Width(40)))
                {
                    JumpToPhrase(displayer, _targetPhraseIndex);
                }
                GUI.enabled = true;

                EditorGUILayout.EndHorizontal();

                // Sentence jump
                if (_targetPhraseIndex >= 0 && _targetPhraseIndex < currentDoc.Phrases.Count)
                {
                    var targetPhrase = currentDoc.Phrases[_targetPhraseIndex];
                    if (targetPhrase.Sentences != null && targetPhrase.Sentences.Count > 0)
                    {
                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField("Jump to Sentence:", GUILayout.Width(100));
                        _targetSentenceIndex = EditorGUILayout.IntSlider(_targetSentenceIndex, 0, targetPhrase.Sentences.Count - 1);

                        GUI.enabled = _targetSentenceIndex >= 0 && _targetSentenceIndex < targetPhrase.Sentences.Count;
                        if (GUILayout.Button("Go", GUILayout.Width(40)))
                        {
                            JumpToSentence(displayer, _targetPhraseIndex, _targetSentenceIndex);
                        }
                        GUI.enabled = true;

                        EditorGUILayout.EndHorizontal();

                        // Preview of target sentence
                        if (_targetSentenceIndex >= 0 && _targetSentenceIndex < targetPhrase.Sentences.Count)
                        {
                            var targetSentence = targetPhrase.Sentences[_targetSentenceIndex];
                            EditorGUILayout.LabelField("Preview:", targetSentence.Text ?? "");
                        }
                    }
                }

                EditorGUILayout.Space(5);

                // Quick navigation buttons
                EditorGUILayout.LabelField("Quick Navigation", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();

                if (GUILayout.Button("⏮️ First Phrase"))
                {
                    JumpToPhrase(displayer, 0);
                }

                if (GUILayout.Button("⏭️ Last Phrase"))
                {
                    JumpToPhrase(displayer, currentDoc.Phrases.Count - 1);
                }

                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();

                if (GUILayout.Button("⏪ First Sentence"))
                {
                    JumpToSentence(displayer, displayer.GetCurrentPhraseIndex(), 0);
                }

                if (GUILayout.Button("⏩ Last Sentence"))
                {
                    var currentPhraseIndex = displayer.GetCurrentPhraseIndex();
                    if (currentPhraseIndex >= 0 && currentPhraseIndex < currentDoc.Phrases.Count)
                    {
                        var currentPhrase = currentDoc.Phrases[currentPhraseIndex];
                        if (currentPhrase.Sentences != null && currentPhrase.Sentences.Count > 0)
                        {
                            JumpToSentence(displayer, currentPhraseIndex, currentPhrase.Sentences.Count - 1);
                        }
                    }
                }

                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("No document loaded or document has no phrases.", MessageType.Info);
            }

            EditorGUI.indentLevel--;
        }
    }

    private void JumpToPhrase(VisualTextDisplayer displayer, int phraseIndex)
    {
        // Temporarily set the current indices and display the phrase
        var currentDoc = displayer._currentDocument;
        if (currentDoc != null && phraseIndex >= 0 && phraseIndex < currentDoc.Phrases.Count)
        {
            // Create a temporary document with just the target phrase
            var tempDocument = ScriptableObject.CreateInstance<VisualTextDocument>();
            tempDocument.name = $"TempDocument_Phrase_{phraseIndex}";
            tempDocument.Phrases.Add(currentDoc.Phrases[phraseIndex]);

            // Copy settings from original document
            CopyDocumentSettings(currentDoc, tempDocument);

            displayer.Display(tempDocument);

            // Update target indices
            _targetPhraseIndex = phraseIndex;
            _targetSentenceIndex = 0;
        }
    }

    private void JumpToSentence(VisualTextDisplayer displayer, int phraseIndex, int sentenceIndex)
    {
        var currentDoc = displayer._currentDocument;
        if (currentDoc != null && phraseIndex >= 0 && phraseIndex < currentDoc.Phrases.Count)
        {
            var targetPhrase = currentDoc.Phrases[phraseIndex];
            if (sentenceIndex >= 0 && sentenceIndex < targetPhrase.Sentences.Count)
            {
                // Create a temporary document with just the target sentence
                var tempDocument = ScriptableObject.CreateInstance<VisualTextDocument>();
                tempDocument.name = $"TempDocument_Sentence_{phraseIndex}_{sentenceIndex}";

                var tempPhrase = new PhraseData();
                tempPhrase.Sentences.Add(targetPhrase.Sentences[sentenceIndex]);
                tempDocument.Phrases.Add(tempPhrase);

                // Copy settings from original document
                CopyDocumentSettings(currentDoc, tempDocument);

                displayer.Display(tempDocument);

                // Update target indices
                _targetPhraseIndex = phraseIndex;
                _targetSentenceIndex = sentenceIndex;
            }
        }
    }

    private void CopyDocumentSettings(VisualTextDocument source, VisualTextDocument target)
    {
        if (source == null || target == null) return;

        target.DefaultTextColor = source.DefaultTextColor;
        target.DefaultFontAsset = source.DefaultFontAsset;
        target.DefaultFontSize = source.DefaultFontSize;
        target.DefaultIsBold = source.DefaultIsBold;
        target.DefaultIsItalic = source.DefaultIsItalic;
        target.DefaultAlignment = source.DefaultAlignment;
        target.DefaultDelayAfterSentence = source.DefaultDelayAfterSentence;
        target.DefaultUseTypewriter = source.DefaultUseTypewriter;
        target.DefaultDelayPerCharacter = source.DefaultDelayPerCharacter;
        target.DefaultPauseOnPunctuation = source.DefaultPauseOnPunctuation;
        target.DefaultPunctuationDelay = source.DefaultPunctuationDelay;
        target.DefaultTypewriterSound = source.DefaultTypewriterSound;
    }

    private void DrawStatusTab(VisualTextDisplayer displayer)
    {
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("Status information is only available during runtime.", MessageType.Info);
            return;
        }

        // Auto-refresh controls
        DrawSectionHeader("🔄 Refresh Settings");
        EditorGUILayout.BeginHorizontal();
        _autoRefreshStatus = EditorGUILayout.Toggle("Auto Refresh", _autoRefreshStatus);

        if (GUILayout.Button("🔄 Refresh Now"))
        {
            Repaint();
        }
        EditorGUILayout.EndHorizontal();

        if (_autoRefreshStatus && EditorApplication.timeSinceStartup - _lastRefreshTime > 0.1)
        {
            _lastRefreshTime = EditorApplication.timeSinceStartup;
            Repaint();
        }

        EditorGUILayout.Space(10);

        // Real-time Status Display
        DrawRealTimeStatus(displayer);

        EditorGUILayout.Space(10);

        // Document Analysis
        DrawDocumentAnalysis(displayer);

        EditorGUILayout.Space(10);

        // Component Status
        DrawComponentStatus(displayer);
    }

    private void DrawRealTimeStatus(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("📊 Real-Time Status");

        // Current State with colors
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("State:", GUILayout.Width(80));

        string state = "Idle";
        Color stateColor = Color.gray;

        if (displayer.IsDisplaying)
        {
            if (displayer.IsPlaying())
            {
                state = "Playing";
                stateColor = Color.green;
            }
            else if (displayer.IsPaused())
            {
                state = "Paused";
                stateColor = Color.yellow;
            }
            else
            {
                state = "Displaying";
                stateColor = Color.cyan;
            }
        }

        var oldColor = GUI.color;
        GUI.color = stateColor;
        EditorGUILayout.LabelField($"● {state}", EditorStyles.boldLabel);
        GUI.color = oldColor;

        EditorGUILayout.EndHorizontal();

        // Additional status flags
        EditorGUI.BeginDisabledGroup(true);
        EditorGUILayout.Toggle("Is Displaying", displayer.IsDisplaying);
        EditorGUILayout.Toggle("Fast Forward Active", displayer.IsFastForwardActive());
        EditorGUI.EndDisabledGroup();
    }

    private void DrawDocumentAnalysis(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("📄 Document Analysis");

        var currentDoc = displayer._currentDocument;
        if (currentDoc != null)
        {
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ObjectField("Loaded Document", currentDoc, typeof(VisualTextDocument), false);
            EditorGUI.EndDisabledGroup();

            // Document statistics
            int totalPhrases = currentDoc.Phrases?.Count ?? 0;
            int totalSentences = 0;
            int totalCharacters = 0;

            if (currentDoc.Phrases != null)
            {
                foreach (var phrase in currentDoc.Phrases)
                {
                    if (phrase.Sentences != null)
                    {
                        totalSentences += phrase.Sentences.Count;
                        foreach (var sentence in phrase.Sentences)
                        {
                            totalCharacters += sentence.Text?.Length ?? 0;
                        }
                    }
                }
            }

            EditorGUILayout.LabelField("Statistics", EditorStyles.boldLabel);
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.IntField("Total Phrases", totalPhrases);
            EditorGUILayout.IntField("Total Sentences", totalSentences);
            EditorGUILayout.IntField("Total Characters", totalCharacters);
            EditorGUI.EndDisabledGroup();

            // Current position
            int currentPhraseIndex = displayer.GetCurrentPhraseIndex();
            int currentSentenceIndex = displayer.GetCurrentSentenceIndex();

            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Current Position", EditorStyles.boldLabel);

            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.IntField("Phrase Index", currentPhraseIndex);
            EditorGUILayout.LabelField("Phrase Progress", $"{currentPhraseIndex + 1} / {totalPhrases}");

            if (currentPhraseIndex >= 0 && currentPhraseIndex < totalPhrases)
            {
                var currentPhrase = currentDoc.Phrases[currentPhraseIndex];
                int sentencesInPhrase = currentPhrase.Sentences?.Count ?? 0;

                EditorGUILayout.IntField("Sentence Index", currentSentenceIndex);
                EditorGUILayout.LabelField("Sentence Progress", $"{currentSentenceIndex + 1} / {sentencesInPhrase}");

                if (currentSentenceIndex >= 0 && currentSentenceIndex < sentencesInPhrase)
                {
                    var currentSentence = currentPhrase.Sentences[currentSentenceIndex];
                    EditorGUILayout.LabelField("Current Text", currentSentence.Text ?? "");
                    EditorGUILayout.LabelField("Text Length", (currentSentence.Text?.Length ?? 0).ToString());
                }
            }
            EditorGUI.EndDisabledGroup();

            // Progress bars
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Progress", EditorStyles.boldLabel);

            if (totalPhrases > 0)
            {
                float phraseProgress = (float)(currentPhraseIndex + 1) / totalPhrases;
                EditorGUILayout.LabelField($"Phrase Progress: {phraseProgress:P1}");
                EditorGUI.ProgressBar(EditorGUILayout.GetControlRect(), phraseProgress, $"{currentPhraseIndex + 1}/{totalPhrases}");
            }

            if (currentPhraseIndex >= 0 && currentPhraseIndex < totalPhrases)
            {
                var currentPhrase = currentDoc.Phrases[currentPhraseIndex];
                int sentencesInPhrase = currentPhrase.Sentences?.Count ?? 0;

                if (sentencesInPhrase > 0)
                {
                    float sentenceProgress = (float)(currentSentenceIndex + 1) / sentencesInPhrase;
                    EditorGUILayout.LabelField($"Sentence Progress: {sentenceProgress:P1}");
                    EditorGUI.ProgressBar(EditorGUILayout.GetControlRect(), sentenceProgress, $"{currentSentenceIndex + 1}/{sentencesInPhrase}");
                }
            }
        }
        else
        {
            EditorGUILayout.HelpBox("No document currently loaded.", MessageType.Info);
        }
    }

    private void DrawComponentStatus(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("🔧 Component Status");

        // TextMeshPro status
        EditorGUILayout.LabelField("TextMeshPro Component", EditorStyles.boldLabel);
        EditorGUI.BeginDisabledGroup(true);
        EditorGUILayout.Toggle("Available", displayer.textComponent != null);

        if (displayer.textComponent != null)
        {
            EditorGUILayout.LabelField("Current Text Length", displayer.textComponent.text?.Length.ToString() ?? "0");
            EditorGUILayout.LabelField("Max Visible Characters", displayer.textComponent.maxVisibleCharacters.ToString());
            EditorGUILayout.LabelField("Font Size", displayer.textComponent.fontSize.ToString("F1"));
            EditorGUILayout.ColorField("Text Color", displayer.textComponent.color);
        }
        EditorGUI.EndDisabledGroup();

        EditorGUILayout.Space(5);

        // AudioSource status
        EditorGUILayout.LabelField("AudioSource Component", EditorStyles.boldLabel);
        EditorGUI.BeginDisabledGroup(true);
        EditorGUILayout.Toggle("Available", displayer.audioSource != null);

        if (displayer.audioSource != null)
        {
            EditorGUILayout.Toggle("Is Playing", displayer.audioSource.isPlaying);
            EditorGUILayout.Slider("Volume", displayer.audioSource.volume, 0f, 1f);
            EditorGUILayout.Toggle("Mute", displayer.audioSource.mute);
        }
        EditorGUI.EndDisabledGroup();
    }

    private void DrawSectionHeader(string title)
    {
        EditorGUILayout.Space(5);

        Rect headerRect = EditorGUILayout.GetControlRect(false, 20);
        EditorGUI.DrawRect(headerRect, _successColor);

        var style = new GUIStyle(EditorStyles.boldLabel);
        style.alignment = TextAnchor.MiddleLeft;

        EditorGUI.LabelField(new Rect(headerRect.x + 5, headerRect.y, headerRect.width - 5, headerRect.height),
                            title, style);

        EditorGUILayout.Space(2);
    }
}
