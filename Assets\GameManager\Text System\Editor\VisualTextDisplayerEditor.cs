using UnityEngine;
using UnityEditor;
using TMPro;

/// <summary>
/// Editor customizado para o Visual Text Displayer que organiza as propriedades em abas
/// e fornece controles de teste e informações de estado.
/// </summary>
[CustomEditor(typeof(VisualTextDisplayer), true)]
public class VisualTextDisplayerEditor : Editor
{
    private int _tabIndex = 0;
    private readonly string[] _tabNames = { "📝 Setup", "⚙️ Settings", "🎮 Controls", "📊 Status" };
    
    // Cores para UI
    private readonly Color _headerColor = new Color(0.2f, 0.4f, 0.8f, 0.3f);
    private readonly Color _successColor = new Color(0.2f, 0.8f, 0.2f, 0.3f);
    private readonly Color _warningColor = new Color(0.8f, 0.6f, 0.2f, 0.3f);
    private readonly Color _errorColor = new Color(0.8f, 0.2f, 0.2f, 0.3f);
    private readonly Color _tabBackgroundColor = new Color(0.9f, 0.9f, 0.9f, 0.1f);
    
    // Estado para teste
    private string _testText = "Hello, World! This is a test message.";
    private VisualTextDocument _testDocument;
    
    // Foldouts
    private bool _showEvents = false;
    private bool _showDefaultSettings = true;
    private bool _showFastForwardSettings = true;
    
    public override void OnInspectorGUI()
    {
        VisualTextDisplayer displayer = (VisualTextDisplayer)target;
        serializedObject.Update();
        
        DrawHeader(displayer);
        DrawTabs();
        
        EditorGUILayout.Space(5);
        
        switch (_tabIndex)
        {
            case 0: DrawSetupTab(); break;
            case 1: DrawSettingsTab(); break;
            case 2: DrawControlsTab(displayer); break;
            case 3: DrawStatusTab(displayer); break;
        }
        
        if (GUI.changed)
        {
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(displayer);
        }
    }
    
    private void DrawHeader(VisualTextDisplayer displayer)
    {
        // Header com fundo colorido
        Rect headerRect = EditorGUILayout.GetControlRect(false, 60);
        EditorGUI.DrawRect(headerRect, _headerColor);
        
        GUILayout.BeginArea(headerRect);
        GUILayout.Space(8);
        
        GUILayout.BeginHorizontal();
        GUILayout.Space(10);
        
        // Ícone e título
        GUILayout.BeginVertical();
        GUILayout.Label("🎭 Visual Text Displayer", EditorStyles.largeLabel);
        
        // Status rápido
        string status = "Idle";
        Color statusColor = Color.gray;
        
        if (Application.isPlaying && displayer != null)
        {
            if (displayer.IsDisplaying)
            {
                status = displayer.IsPlaying() ? "Playing" : (displayer.IsPaused() ? "Paused" : "Displaying");
                statusColor = displayer.IsPlaying() ? Color.green : (displayer.IsPaused() ? Color.yellow : Color.cyan);
            }
        }
        
        var oldColor = GUI.color;
        GUI.color = statusColor;
        GUILayout.Label($"Status: {status}", EditorStyles.miniLabel);
        GUI.color = oldColor;
        
        GUILayout.EndVertical();
        
        GUILayout.FlexibleSpace();
        
        // Validação rápida
        if (displayer.textComponent == null)
        {
            GUI.color = Color.red;
            GUILayout.Label("⚠️ No TextMeshPro", EditorStyles.boldLabel);
            GUI.color = oldColor;
        }
        else
        {
            GUI.color = Color.green;
            GUILayout.Label("✅ Ready", EditorStyles.boldLabel);
            GUI.color = oldColor;
        }
        
        GUILayout.Space(10);
        GUILayout.EndHorizontal();
        
        GUILayout.EndArea();
    }
    
    private void DrawTabs()
    {
        // Fundo das abas
        Rect tabRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(tabRect, _tabBackgroundColor);
        
        _tabIndex = GUI.Toolbar(tabRect, _tabIndex, _tabNames);
    }
    
    private void DrawSetupTab()
    {
        DrawSectionHeader("🔧 Component References");
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("textComponent"), 
            new GUIContent("Text Component", "O componente TextMeshProUGUI onde o texto será exibido"));
        
        EditorGUILayout.PropertyField(serializedObject.FindProperty("audioSource"), 
            new GUIContent("Audio Source", "AudioSource para efeitos sonoros de texto"));
        
        EditorGUILayout.Space(10);
        
        // Validação e auto-setup
        VisualTextDisplayer displayer = (VisualTextDisplayer)target;
        
        if (displayer.textComponent == null)
        {
            EditorGUILayout.HelpBox("TextMeshProUGUI component is required!", MessageType.Error);
            
            if (GUILayout.Button("🔍 Auto-Find TextMeshPro Component"))
            {
                var textComp = displayer.GetComponent<TextMeshProUGUI>();
                if (textComp != null)
                {
                    serializedObject.FindProperty("textComponent").objectReferenceValue = textComp;
                    serializedObject.ApplyModifiedProperties();
                }
                else
                {
                    EditorUtility.DisplayDialog("Not Found", 
                        "No TextMeshProUGUI component found on this GameObject. Please add one first.", "OK");
                }
            }
        }
        
        if (displayer.audioSource == null)
        {
            EditorGUILayout.HelpBox("AudioSource is optional but recommended for typewriter sounds.", MessageType.Info);
            
            if (GUILayout.Button("🔍 Auto-Find AudioSource Component"))
            {
                var audioComp = displayer.GetComponent<AudioSource>();
                if (audioComp != null)
                {
                    serializedObject.FindProperty("audioSource").objectReferenceValue = audioComp;
                    serializedObject.ApplyModifiedProperties();
                }
                else
                {
                    if (EditorUtility.DisplayDialog("Add AudioSource?", 
                        "No AudioSource found. Would you like to add one?", "Yes", "No"))
                    {
                        var newAudioSource = displayer.gameObject.AddComponent<AudioSource>();
                        newAudioSource.playOnAwake = false;
                        serializedObject.FindProperty("audioSource").objectReferenceValue = newAudioSource;
                        serializedObject.ApplyModifiedProperties();
                    }
                }
            }
        }
        
        EditorGUILayout.Space(10);
        
        // Events section
        DrawSectionHeader("📡 Events");
        _showEvents = EditorGUILayout.Foldout(_showEvents, "Event Callbacks", true);
        
        if (_showEvents)
        {
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnDisplayStarted"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnDisplayFinished"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnPhraseStarted"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnPhraseFinished"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnSentenceStarted"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("OnSentenceFinished"));
            EditorGUI.indentLevel--;
        }
    }
    
    private void DrawSettingsTab()
    {
        // Fast Forward Settings
        DrawSectionHeader("⚡ Fast Forward Settings");
        _showFastForwardSettings = EditorGUILayout.Foldout(_showFastForwardSettings, "Fast Forward Configuration", true);
        
        if (_showFastForwardSettings)
        {
            EditorGUI.indentLevel++;
            EditorGUILayout.PropertyField(serializedObject.FindProperty("fastForwardTypewriterMultiplier"), 
                new GUIContent("Typewriter Multiplier", "Multiplicador de velocidade para o typewriter durante fast forward"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("fastForwardAnimationMultiplier"), 
                new GUIContent("Animation Multiplier", "Multiplicador de velocidade para animações durante fast forward"));
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space(10);
        
        // Default Settings for Simple Text
        DrawSectionHeader("📝 Default Settings for Simple Text");
        _showDefaultSettings = EditorGUILayout.Foldout(_showDefaultSettings, "Simple Text Configuration", true);
        
        if (_showDefaultSettings)
        {
            EditorGUI.indentLevel++;
            
            // Visual settings
            EditorGUILayout.LabelField("Visual Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultTextColor"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultFontAsset"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultFontSize"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultIsBold"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultIsItalic"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultAlignment"));
            
            EditorGUILayout.Space(5);
            
            // Timing settings
            EditorGUILayout.LabelField("Timing Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultDelayAfterSentence"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultDelayAfterPhrase"));
            
            EditorGUILayout.Space(5);
            
            // Typewriter settings
            EditorGUILayout.LabelField("Typewriter Settings", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultUseTypewriter"));
            
            if (serializedObject.FindProperty("defaultUseTypewriter").boolValue)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultDelayPerCharacter"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultPauseOnPunctuation"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultPunctuationDelay"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("defaultTypewriterSound"));
                EditorGUI.indentLevel--;
            }
            
            EditorGUI.indentLevel--;
        }
    }

    private void DrawControlsTab(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("🎮 Test Controls");

        EditorGUILayout.HelpBox("Use these controls to test the Visual Text Displayer functionality in the editor.", MessageType.Info);

        EditorGUILayout.Space(5);

        // Test Text Input
        EditorGUILayout.LabelField("Simple Text Test", EditorStyles.boldLabel);
        _testText = EditorGUILayout.TextArea(_testText, GUILayout.Height(60));

        EditorGUILayout.BeginHorizontal();

        GUI.enabled = !string.IsNullOrEmpty(_testText) && displayer.textComponent != null;
        if (GUILayout.Button("▶️ Display Simple Text"))
        {
            if (Application.isPlaying)
            {
                displayer.Display(_testText);
            }
            else
            {
                displayer.DisplayFromEditor(_testText);
            }
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(10);

        // Document Test
        EditorGUILayout.LabelField("Document Test", EditorStyles.boldLabel);
        _testDocument = (VisualTextDocument)EditorGUILayout.ObjectField("Test Document", _testDocument, typeof(VisualTextDocument), false);

        EditorGUILayout.BeginHorizontal();

        GUI.enabled = _testDocument != null && displayer.textComponent != null;
        if (GUILayout.Button("▶️ Display Document"))
        {
            if (Application.isPlaying)
            {
                displayer.Display(_testDocument);
            }
            else
            {
                displayer.DisplayFromEditor(_testDocument);
            }
        }
        GUI.enabled = true;

        EditorGUILayout.EndHorizontal();

        EditorGUILayout.Space(10);

        // Playback Controls (Runtime only)
        if (Application.isPlaying)
        {
            DrawSectionHeader("🎛️ Playback Controls");

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("⏮️ Previous Phrase"))
            {
                displayer.PreviousPhrase();
            }

            if (GUILayout.Button("⏭️ Next Phrase"))
            {
                displayer.NextPhrase();
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("⏪ Previous Sentence"))
            {
                displayer.PreviousSentence();
            }

            if (GUILayout.Button("⏩ Next Sentence"))
            {
                displayer.NextSentence();
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            EditorGUILayout.BeginHorizontal();

            if (displayer.IsPlaying())
            {
                if (GUILayout.Button("⏸️ Pause"))
                {
                    displayer.Pause();
                }
            }
            else
            {
                if (GUILayout.Button("▶️ Play"))
                {
                    displayer.Play();
                }
            }

            if (GUILayout.Button("⏹️ Stop"))
            {
                displayer.Stop();
            }

            if (GUILayout.Button("🗑️ Clear"))
            {
                displayer.Clear();
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("⏭️ Skip"))
            {
                displayer.Skip();
            }

            string fastForwardText = displayer.IsFastForwardActive() ? "🐌 Normal Speed" : "⚡ Fast Forward";
            if (GUILayout.Button(fastForwardText))
            {
                displayer.ToggleFastForward();
            }

            EditorGUILayout.EndHorizontal();
        }
        else
        {
            EditorGUILayout.HelpBox("Playback controls are only available during runtime.", MessageType.Info);
        }

        EditorGUILayout.Space(10);

        // Clear Controls
        DrawSectionHeader("🧹 Clear Controls");

        EditorGUILayout.BeginHorizontal();

        if (GUILayout.Button("🗑️ Clear Text"))
        {
            if (displayer.textComponent != null)
            {
                displayer.textComponent.text = "";
            }
        }

        if (GUILayout.Button("⏹️ Stop All"))
        {
            displayer.StopDisplay();
        }

        EditorGUILayout.EndHorizontal();
    }

    private void DrawStatusTab(VisualTextDisplayer displayer)
    {
        DrawSectionHeader("📊 Current Status");

        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("Status information is only available during runtime.", MessageType.Info);
            return;
        }

        // Current State
        EditorGUILayout.LabelField("Current State", EditorStyles.boldLabel);

        EditorGUI.BeginDisabledGroup(true);

        EditorGUILayout.Toggle("Is Displaying", displayer.IsDisplaying);
        EditorGUILayout.Toggle("Is Playing", displayer.IsPlaying());
        EditorGUILayout.Toggle("Is Paused", displayer.IsPaused());
        EditorGUILayout.Toggle("Fast Forward Active", displayer.IsFastForwardActive());

        EditorGUI.EndDisabledGroup();

        EditorGUILayout.Space(10);

        // Current Document Info
        var currentDoc = displayer.GetCurrentDocument();
        if (currentDoc != null)
        {
            DrawSectionHeader("📄 Current Document");

            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.ObjectField("Document", currentDoc, typeof(VisualTextDocument), false);
            EditorGUILayout.IntField("Total Phrases", currentDoc.Phrases?.Count ?? 0);
            EditorGUI.EndDisabledGroup();

            EditorGUILayout.Space(5);

            // Navigation Info
            EditorGUILayout.LabelField("Navigation Position", EditorStyles.boldLabel);

            EditorGUI.BeginDisabledGroup(true);
            int currentPhraseIndex = displayer.GetCurrentPhraseIndex();
            int currentSentenceIndex = displayer.GetCurrentSentenceIndex();

            EditorGUILayout.IntField("Current Phrase Index", currentPhraseIndex);
            EditorGUILayout.LabelField("Phrase Progress", $"{currentPhraseIndex + 1} / {currentDoc.Phrases?.Count ?? 0}");

            if (currentPhraseIndex >= 0 && currentPhraseIndex < currentDoc.Phrases.Count)
            {
                var currentPhrase = currentDoc.Phrases[currentPhraseIndex];
                EditorGUILayout.IntField("Current Sentence Index", currentSentenceIndex);
                EditorGUILayout.LabelField("Sentence Progress", $"{currentSentenceIndex + 1} / {currentPhrase.Sentences?.Count ?? 0}");

                if (currentSentenceIndex >= 0 && currentSentenceIndex < currentPhrase.Sentences.Count)
                {
                    var currentSentence = currentPhrase.Sentences[currentSentenceIndex];
                    EditorGUILayout.LabelField("Current Sentence", currentSentence.Text ?? "");
                }
            }

            EditorGUI.EndDisabledGroup();
        }
        else
        {
            EditorGUILayout.HelpBox("No document currently loaded.", MessageType.Info);
        }

        EditorGUILayout.Space(10);

        // Component Status
        DrawSectionHeader("🔧 Component Status");

        EditorGUI.BeginDisabledGroup(true);

        EditorGUILayout.Toggle("TextMeshPro Available", displayer.textComponent != null);
        EditorGUILayout.Toggle("AudioSource Available", displayer.audioSource != null);

        if (displayer.textComponent != null)
        {
            EditorGUILayout.LabelField("Current Text Length", displayer.textComponent.text?.Length.ToString() ?? "0");
            EditorGUILayout.LabelField("Max Visible Characters", displayer.textComponent.maxVisibleCharacters.ToString());
        }

        if (displayer.audioSource != null)
        {
            EditorGUILayout.Toggle("Audio Playing", displayer.audioSource.isPlaying);
        }

        EditorGUI.EndDisabledGroup();

        EditorGUILayout.Space(10);

        // Refresh button
        if (GUILayout.Button("🔄 Refresh Status"))
        {
            Repaint();
        }
    }

    private void DrawSectionHeader(string title)
    {
        EditorGUILayout.Space(5);

        Rect headerRect = EditorGUILayout.GetControlRect(false, 20);
        EditorGUI.DrawRect(headerRect, _successColor);

        var style = new GUIStyle(EditorStyles.boldLabel);
        style.alignment = TextAnchor.MiddleLeft;

        EditorGUI.LabelField(new Rect(headerRect.x + 5, headerRect.y, headerRect.width - 5, headerRect.height),
                            title, style);

        EditorGUILayout.Space(2);
    }
}
