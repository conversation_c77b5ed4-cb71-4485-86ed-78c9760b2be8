-target:library
-out:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_0_42
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:UNITY_POST_PROCESSING_STACK_V2
-define:DOTWEEN
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/Packages/Microsoft.Bcl.AsyncInterfaces.9.0.3/lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll"
-r:"Assets/Packages/Newtonsoft.Json.13.0.3/lib/netstandard2.0/Newtonsoft.Json.dll"
-r:"Assets/Packages/System.IO.Pipelines.9.0.3/lib/netstandard2.0/System.IO.Pipelines.dll"
-r:"Assets/Packages/System.Runtime.CompilerServices.Unsafe.6.0.0/lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Assets/Packages/System.Text.Encodings.Web.9.0.3/lib/netstandard2.0/System.Text.Encodings.Web.dll"
-r:"Assets/Packages/System.Text.Json.9.0.3/lib/netstandard2.0/System.Text.Json.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"F:/6000.0.42f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@60ef35ffd3cd/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.visualscripting@b4d700247d4b/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Autodesk.Fbx.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/InputHintEditor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/NuGetForUnity.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/SerializableGUID.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Animation.Rigging.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AppUI.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AppUI.Navigation.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AppUI.Redux.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.AppUI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Behavior.Authoring.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Behavior.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Behavior.GraphFramework.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Behavior.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Behavior.Serialization.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Behavior.Serialization.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Bindings.OpenImageIO.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Cinemachine.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.EditorCoroutines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Alembic.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Formats.Fbx.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.InGameHints.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Localization.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Muse.Behavior.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AddOns.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.AssetIdRemapUtility.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ProBuilder.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Base.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Recorder.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Sequences.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Splines.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.InputSystem.Samples.UIvsGameInput.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/XNode.ref.dll"
-r:"Library/Bee/artifacts/1900b0aE.dag/XNodeEditor.ref.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"Assets/Packages/System.Text.Json.9.0.3/analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll"
-analyzer:"F:/6000.0.42f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"F:/6000.0.42f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"F:/6000.0.42f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
-analyzer:"Library/PackageCache/com.unity.dt.app-ui@166eda91cea7/Runtime/SourceGenerators/netstandard2.0/EnumToLowerCase.dll"
"Assets/Custom Attributes/Create Instance SO.cs"
"Assets/Custom Attributes/Subclass Selector.cs"
"Assets/Event System/Event System With Scriptable Object/Condition with Scriptable Object/Base Condition SO.cs"
"Assets/Event System/Event System With Scriptable Object/Condition with Scriptable Object/Generic Condition SO.cs"
"Assets/Event System/Event System With Scriptable Object/Condition with Scriptable Object/Player Conditions/Health Condition SO.cs"
"Assets/Event System/Event System With Scriptable Object/Condition with Scriptable Object/Player Conditions/Heart Rate Condition SO.cs"
"Assets/Event System/Event System With Scriptable Object/Condition with Scriptable Object/Player Conditions/Sanity Condition SO.cs"
"Assets/Event System/Event System With Scriptable Object/Condition with Scriptable Object/Player Conditions/Stamina Condition SO.cs"
"Assets/Event System/Event System With Scriptable Object/Condition with Scriptable Object/Player Conditions/Status Condition SO.cs"
"Assets/Event System/Event System With Scriptable Object/Events with Scriptable Object/Base Event SO.cs"
"Assets/Event System/Event System With Scriptable Object/Events with Scriptable Object/Generic Event SO.cs"
"Assets/Event System/Event System With Scriptable Object/Events with Scriptable Object/Player Events/UI Events/Instantiate UI Element.cs"
"Assets/Event System/Event System With System Reflection/My Condition System Reflection/Listener Condition.cs"
"Assets/Event System/Event System With System Reflection/My Condition System Reflection/My Custom Condition.cs"
"Assets/Event System/Event System With System Reflection/My Unity Event System/Custom Unity Event Utils.cs"
"Assets/Event System/Event System With System Reflection/My Unity Event System/Listener Data.cs"
"Assets/Event System/Event System With System Reflection/My Unity Event System/Parameter Data.cs"
"Assets/Event System/Event System With System Reflection/My Unity Event System/Return Data.cs"
"Assets/Event System/Event System With System Reflection/My Unity Event.cs"
"Assets/GameManager/Addresables/AddressableAssetExtension.cs"
"Assets/GameManager/Audio System/AudioEffect.cs"
"Assets/GameManager/Audio System/MultiClipAudioEffect.cs"
"Assets/GameManager/Audio System/SimpleAudioEffect.cs"
"Assets/GameManager/Custom Scriptable Object System/InstanceSODrawer.cs"
"Assets/GameManager/Custom Scriptable Object System/InstantiableSO.cs"
"Assets/GameManager/Events/Callback Config Base.cs"
"Assets/GameManager/Events/Callback Config.cs"
"Assets/GameManager/Events/Callback Item.cs"
"Assets/GameManager/Events/Method Data.cs"
"Assets/GameManager/Events/Triggers/Vision Event Trigger.cs"
"Assets/GameManager/Finite State Machine/Base State.cs"
"Assets/GameManager/Finite State Machine/IState.cs"
"Assets/GameManager/Finite State Machine/Player/Player Basic States.cs"
"Assets/GameManager/Input System/Examples/IEnumeratorCallbackExample.cs"
"Assets/GameManager/Input System/Examples/UnregisterIEnumeratorExample.cs"
"Assets/GameManager/Input System/Input Condition System/Input Condition SO.cs"
"Assets/GameManager/Input System/Input Context Factory.cs"
"Assets/GameManager/Input System/Input Hint/Runtime/Bind Hint Config.cs"
"Assets/GameManager/Input System/Input Hint/Runtime/Device Group Hint Config.cs"
"Assets/GameManager/Input System/Input Hint/Runtime/Hint Icon Info.cs"
"Assets/GameManager/Input System/Input Hint/Runtime/Input Hint Service.cs"
"Assets/GameManager/Input System/Input Hint/Runtime/Input Hints Config.cs"
"Assets/GameManager/Input System/Input Hint/Runtime/Scheme Hint Config.cs"
"Assets/GameManager/Input System/Input Interaction Attribute.cs"
"Assets/GameManager/Input System/Input Interactions/My Holding Timer.cs"
"Assets/GameManager/Input System/Player Inputs Config.cs"
"Assets/GameManager/Input System/Player/Action Activation Context.cs"
"Assets/GameManager/Input System/Player/ExemploCallbacksDiretos.cs"
"Assets/GameManager/Input System/Player/ExemploCurrentActionMap.cs"
"Assets/GameManager/Input System/Player/ExemploSendMessages.cs"
"Assets/GameManager/Input System/Player/Input Action Config.cs"
"Assets/GameManager/Input System/Player/Input Call Config.cs"
"Assets/GameManager/Input System/Player/Input Event Binding Finder.cs"
"Assets/GameManager/Input System/Player/Input State.cs"
"Assets/GameManager/Input System/Player/Player Input Handler Blocks.cs"
"Assets/GameManager/Input System/Player/PlayerInputHandler.cs"
"Assets/GameManager/Input System/Player/PlayerInputHandler.Registration.cs"
"Assets/GameManager/Interfaces/IGameEntity.cs"
"Assets/GameManager/Interfaces/INPC.cs"
"Assets/GameManager/Interfaces/IPlayer.cs"
"Assets/GameManager/Json Scripts/Contract Resolver/Ignore Classes Contract Resolver.cs"
"Assets/GameManager/Json Scripts/Contract Resolver/Only Classes Contract Resolver.cs"
"Assets/GameManager/Json Scripts/Json Converters/Asset Reference Converter.cs"
"Assets/GameManager/Json Scripts/Json Converters/Quaternion Converter.cs"
"Assets/GameManager/Json Scripts/Json Converters/Transform Converter.cs"
"Assets/GameManager/Json Scripts/Json Converters/Vectors Converter.cs"
"Assets/GameManager/Json Scripts/Json Serializer Settings.cs"
"Assets/GameManager/Json Scripts/Serialized Unity Scripts/Serializable Date Time and Time Span.cs"
"Assets/GameManager/Json Scripts/Serialized Unity Scripts/Serializable GameObject.cs"
"Assets/GameManager/Json Scripts/Serialized Unity Scripts/Serializable Transform.cs"
"Assets/GameManager/Json Scripts/Serialized Unity Scripts/Serializable Vectors.cs"
"Assets/GameManager/Main Menu/Main Menu.cs"
"Assets/GameManager/OptionalPropertyDrawer.cs"
"Assets/GameManager/Save/Save Data Struct.cs"
"Assets/GameManager/Save/Save Files Manager.cs"
"Assets/GameManager/Save/Save System Testers/Test GameObject Save.cs"
"Assets/GameManager/Save/SaveManager.cs"
"Assets/GameManager/Save/UI/Load Game Panel Manager.cs"
"Assets/GameManager/Save/UI/Save Slot UI.cs"
"Assets/GameManager/Scene/Scene Datas Struct/Local Scene.cs"
"Assets/GameManager/Scene/Scene Datas Struct/Spawn Point System/Spawn Point Manager Editor.cs"
"Assets/GameManager/Scene/Scene Datas Struct/Spawn Point System/Spawn Point.cs"
"Assets/GameManager/Scene/Scene Datas Struct/Spawn Point System/SpawnPointManager.cs"
"Assets/GameManager/Scene/SceneManager script.cs"
"Assets/GameManager/Scene/Scriptable Object Scene/Scene Data Base SO.cs"
"Assets/GameManager/Scene/Transition Trigger.cs"
"Assets/GameManager/Scripts/GameManager script.cs"
"Assets/GameManager/Scripts/Item List.cs"
"Assets/GameManager/Scripts/MultitionPattern.cs"
"Assets/GameManager/Scripts/Physics Manager.cs"
"Assets/GameManager/Scripts/SingletonPattern.cs"
"Assets/GameManager/Text System/Examples/SimpleTextDisplayExample.cs"
"Assets/GameManager/Text System/Examples/VisualTextDisplayerControlExample.cs"
"Assets/GameManager/Text System/PhraseData.cs"
"Assets/GameManager/Text System/RegexCache.cs"
"Assets/GameManager/Text System/Sentence Display Data.cs"
"Assets/GameManager/Text System/Sentence Info.cs"
"Assets/GameManager/Text System/Test de texto.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Continous Effects/ColorBlendAnimationEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Continous Effects/ShakeAnimationEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Continous Effects/WaveAnimationEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Delete Effects/BurnDeleteEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Delete Effects/DissolveDeleteEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Delete Effects/FadeOutDeleteEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Delete Effects/GlitchDeleteEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Delete Effects/ShrinkDeleteEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/TextAnimationEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Write Effects/BlurInAnimationEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Animation Effect/Write Effects/ScaleInAnimationEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Audio Effect/TextAudioEffect.cs"
"Assets/GameManager/Text System/Text Effects/Text Effect Base.cs"
"Assets/GameManager/Text System/Visual Text Displayer (Player input).cs"
"Assets/GameManager/Text System/Visual Text Displayer.cs"
"Assets/GameManager/Text System/Visual Text Document Editor.cs"
"Assets/GameManager/Text System/Visual Text Document.cs"
"Assets/GameManager/User/User Scripts/Examples/UIMapSwitchExample.cs"
"Assets/GameManager/User/User Scripts/PlayersManager.cs"
"Assets/GameManager/User/User Scripts/User Feedback.cs"
"Assets/GameManager/User/User Scripts/User Menu Manager.cs"
"Assets/GameManager/User/User Scripts/User MonoBehaviour Get interface.cs"
"Assets/GameManager/User/User Scripts/User UI Manager.cs"
"Assets/GameManager/User/User Scripts/UserMonoBehaviour.cs"
"Assets/Inventory/Core/Basic Inventory/Inventory Slot.cs"
"Assets/Inventory/Core/Basic Inventory/Inventory.cs"
"Assets/Inventory/Core/Grid Inventory/Grid Inventory Slot.cs"
"Assets/Inventory/Core/Grid Inventory/Grid Inventory.cs"
"Assets/Inventory/Core/Grid Inventory/Item Data To Rearrange.cs"
"Assets/Inventory/Core/Grid Inventory/Key Journal Inventory Slot.cs"
"Assets/Inventory/Core/Interface/Grid/IInventory Grid Slot.cs"
"Assets/Inventory/Core/Interface/Grid/IInventory Grid.cs"
"Assets/Inventory/Core/Interface/IInventory Slot.cs"
"Assets/Inventory/Core/Interface/IInventory.cs"
"Assets/Inventory/Core/Interface/UI/IInventory Slot UI.cs"
"Assets/Inventory/Core/Interface/UI/IInventory UI.cs"
"Assets/Inventory/Core/Player/Basic Inventory Manager.cs"
"Assets/Inventory/Core/UI/Inventory Slot UI.cs"
"Assets/Inventory/Core/UI/Inventory UI.cs"
"Assets/Modified Unity Components Functions/Custom Instantiate.cs"
"Assets/Modified Unity Components Functions/GridLayout Group Stretchable.cs"
"Assets/Player/Scripts/Player Basic/Player FPS Controller/Camera/FPS Utils.cs"
"Assets/Player/Scripts/Player Basic/Player FPS Controller/Camera/FPSController.cs"
"Assets/Player/Scripts/Player Basic/Player FPS Controller/Camera/Head Bob Manager.cs"
"Assets/Player/Scripts/Player Basic/Player FPS Controller/Camera/Head Bob struct.cs"
"Assets/Player/Scripts/Player Basic/Player FPS Controller/Movement/MovementStatusModifier.cs"
"Assets/Player/Scripts/Player Basic/Player FPS Controller/Movement/PlayerMovimento.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Health Data.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Heart Rate Data.cs"
"Assets/Player/Scripts/Player Basic/Player Status/PlayerStatus.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Sanity Data.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Stamina Data.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/BreathlessEffect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/Dizzines Effect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/EffectManager.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/High Heart Rate Effect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/Low Sanity Trip Effect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/Movement Effects/AdrenalineRushEffect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/Movement Effects/ExhaustionEffect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/Movement Effects/MovementEffectManager.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/Movement Effects/PanicEffect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/StatusEffect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Effects/TiredEffect.cs"
"Assets/Player/Scripts/Player Basic/Player Status/Status Utils.cs"
"Assets/Player/Scripts/Player Basic/PlayerMonobehaviour Funcs.cs"
"Assets/Player/Scripts/Player Basic/PlayerMonoBehaviour.cs"
"Assets/Player/Scripts/Player IK/DynamicIKManager.cs"
"Assets/Player/Scripts/Player IK/IK do Player.cs"
"Assets/Player/Scripts/Player IK/IKTargetConfigurator.cs"
"Assets/Player/Scripts/Player UI/CrosshairHelper.cs"
"Assets/Player/Scripts/Player UI/CrosshairManager.cs"
"Assets/Player/Scripts/Player UI/Hud.cs"
"Assets/Player/Scripts/Player Utils.cs"
"Assets/Props/Items/Item Collectable.cs"
"Assets/Props/Items/Item Examine.cs"
"Assets/Props/Items/Itens Equipaveis/Ferramentas/Lanterna/Lanterna.cs"
"Assets/Props/Items/Scriptable Objects dos items/Consumable Item SO.cs"
"Assets/Props/Items/Scriptable Objects dos items/Equipable Item SO.cs"
"Assets/Props/Items/Scriptable Objects dos items/Item SO.cs"
"Assets/Props/Items/Scriptable Objects dos items/Item Type.cs"
"Assets/Props/Items/Scriptable Objects dos items/Key Item SO.cs"
"Assets/Props/Items/Scriptable Objects dos items/Readable Item SO.cs"
"Assets/Props/Model Info.cs"
"Assets/Props/Props Interface.cs"
"Assets/Props/Scripts/Door/Door.cs"
"Assets/Props/Scripts/Hideout/Hideout.cs"
"Assets/Props/Scripts/Interactions/Generic Interactable.cs"
"Assets/Props/Scripts/Interactions/Interact Hint.cs"
"Assets/Props/Scripts/Interactions/Interactable Object.cs"
"Assets/Props/Scripts/Save/ISaveable.cs"
"Assets/Props/Scripts/Save/SaveableComponent.cs"
"Assets/Props/Scripts/Save/stateful object.cs"
"Assets/Samples/Addressables/2.3.16/Custom Build and Playmode Scripts/LoadSceneForCustomBuild.cs"
"Assets/Samples/Behavior/1.0.10/Runtime Serialization/ChooseTargetPosition.cs"
"Assets/Samples/Behavior/1.0.10/Runtime Serialization/SerializationExampleSceneController.cs"
"Assets/Samples/Behavior/1.0.10/Runtime Serialization/Weapon.cs"
"Assets/Samples/Behavior/1.0.10/Unity Behavior Example/Actions/SetRandomTargetAction.cs"
"Assets/Samples/Behavior/1.0.10/Unity Behavior Example/Actions/TalkAction.cs"
"Assets/Samples/Cinemachine/3.1.3/Input System Samples/Split Screen Multiplayer/ConstantOrbitalRotation.cs"
"Assets/Samples/Cinemachine/3.1.3/Input System Samples/Split Screen Multiplayer/CustomInputHandler.cs"
"Assets/Samples/Cinemachine/3.1.3/Input System Samples/Split Screen Multiplayer/PlayerCounter.cs"
"Assets/Samples/Cinemachine/3.1.3/Input System Samples/Split Screen Multiplayer/PlayerInitializer.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/AimCameraRig.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/AimTargetManager.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/CinemachineFadeOutShaderController.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/CursorLockManager.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/DampedTracker.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/ExpandingAimReticle.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/FlyAround.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/LockPosZ.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/LookAtTarget.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/Magnet.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/MagnetGroupController.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/MixCamerasBasedOnSpeed.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/RandomizedDollySpeed.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/ReduceGroupWeightWhenBelowFloor.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/ReparentPlayerToSurface.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/RigidbodyInterpolationSetter.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/RunnerController.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SamplesDynamicUI.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SamplesHelpUI.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/ShotQualityBasedOnSplineCartPosition.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimpleBullet.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimpleCarController.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimplePlayerAimController.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimplePlayerAnimator.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimplePlayerController.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimplePlayerController2D.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimplePlayerOnSurface.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SimplePlayerShoot.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/SpawnInRadius.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/ThirdPersonFollowCameraSideSwapper.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/TurnAroundPlayer.cs"
"Assets/Samples/Cinemachine/3.1.3/Shared Assets/Scripts/WrapAround.cs"
"Assets/Scripts Utils/Camera Utils/Camera Tools.cs"
"Assets/Scripts Utils/Camera Utils/Camera Utils.cs"
"Assets/Scripts Utils/GameObject Utils/GameObject Utils.cs"
"Assets/Scripts Utils/GameObject Utils/Rect Mask 3D Controller.cs"
"Assets/Scripts Utils/GameObject Utils/RectMask3DControllerEditor.cs"
"Assets/Scripts Utils/Image Utils/Image Utils.cs"
"Assets/Scripts Utils/Layer Utility.cs"
"Assets/Scripts Utils/Mesh Utility.cs"
"Assets/Scripts Utils/Rect Scripts/Mesh Rect Adapter Editor.cs"
"Assets/Scripts Utils/Rect Scripts/Mesh Rect Adapter.cs"
"Assets/Scripts Utils/Rect Scripts/RectTransform Utils.cs"
"Assets/Scripts Utils/Rect Scripts/UI Attacher.cs"
"Assets/Scripts Utils/Scene Utils/Scene Utils.cs"
"Assets/Scripts Utils/String/String Extensions.cs"
"Assets/Scripts Utils/TMPro Utility.cs"
"Assets/Scripts Utils/Transform Scripts/Transform Utils Monobehaviour.cs"
"Assets/Scripts Utils/Transform Scripts/Transform Utils.cs"
"Assets/Scripts Utils/Utils Functions.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark01.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark01_UGUI.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark02.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark03.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/Benchmark04.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/CameraController.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ChatController.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/DropdownSample.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/EnvMapAnimator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ObjectSpin.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/ShaderPropAnimator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/SimpleScript.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/SkewTextExample.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TeleType.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextConsoleSimulator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextMeshProFloatingText.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TextMeshSpawner.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMPro_InstructionOverlay.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_DigitValidator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_ExampleScript_01.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_FrameRateCounter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_PhoneNumberValidator.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextEventCheck.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextEventHandler.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextInfoDebugTool.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextSelector_A.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_TextSelector_B.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/TMP_UiFrameRateCounter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexColorCycler.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexJitter.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexShakeA.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexShakeB.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/VertexZoom.cs"
"Assets/TextMesh Pro/Examples & Extras/Scripts/WarpTextExample.cs"
"Assets/TutorialInfo/Scripts/Readme.cs"
-langversion:9.0
/deterministic
/optimize+
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.UnityAdditionalFile.txt"