using UnityEngine;
using UnityEditor;
using System.Linq;

[CustomEditor(typeof(VisualTextDocument))]
public class VisualTextDocumentEditor : Editor
{
    private bool _showFullText = true;
    private bool _showDefaults = false;
    private bool _showPhrases = true;
    private bool _showStatistics = false;
    
    private Vector2 _scrollPosition;
    private string _searchFilter = "";
    
    private readonly Color _headerColor = new Color(0.2f, 0.4f, 0.8f, 0.3f);
    private readonly Color _successColor = new Color(0.2f, 0.8f, 0.2f, 0.3f);
    private readonly Color _warningColor = new Color(0.8f, 0.6f, 0.2f, 0.3f);
    
    public override void OnInspectorGUI()
    {
        VisualTextDocument document = (VisualTextDocument)target;
        serializedObject.Update();
        
        DrawHeader(document);
        
        _scrollPosition = EditorGUILayout.BeginScrollView(_scrollPosition);
        
        if (_showFullText) DrawFullTextSection(document);
        if (_showDefaults) DrawDefaultsSection();
        if (_showStatistics) DrawStatisticsSection(document);
        if (_showPhrases) DrawPhrasesSection(document);
        
        EditorGUILayout.EndScrollView();
        
        DrawFooter(document);
        
        if (GUI.changed)
        {
            serializedObject.ApplyModifiedProperties();
            EditorUtility.SetDirty(document);
        }
    }
    
    private void DrawHeader(VisualTextDocument document)
    {
        // Header with colored background
        Rect headerRect = EditorGUILayout.GetControlRect(false, 50);
        EditorGUI.DrawRect(headerRect, _headerColor);
        
        GUILayout.BeginArea(headerRect);
        GUILayout.Space(5);
        
        GUILayout.BeginHorizontal();
        GUILayout.Label("📝 Visual Text Document", EditorStyles.largeLabel);
        GUILayout.FlexibleSpace();
        
        if (GUILayout.Button("🖼️ Open Editor", GUILayout.Width(100), GUILayout.Height(30)))
        {
            VisualTextDocumentEditorWindow.ShowWindow(document);
        }
        GUILayout.EndHorizontal();
        
        // Quick stats
        int phraseCount = document.Phrases?.Count ?? 0;
        int sentenceCount = document.Phrases?.Sum(p => p.Sentences?.Count ?? 0) ?? 0;
        GUILayout.Label($"📊 {phraseCount} phrases, {sentenceCount} sentences", EditorStyles.miniLabel);
        
        GUILayout.EndArea();
        GUILayout.Space(5);
    }
    
    private void DrawFullTextSection(VisualTextDocument document)
    {
        // Section header
        Rect sectionRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(sectionRect, _successColor);
        
        _showFullText = EditorGUI.Foldout(new Rect(sectionRect.x + 5, sectionRect.y + 3, sectionRect.width - 100, sectionRect.height), 
                                         _showFullText, "📝 Full Text", true, EditorStyles.foldoutHeader);
        
        // Quick parse button in header
        if (GUI.Button(new Rect(sectionRect.xMax - 90, sectionRect.y + 2, 85, sectionRect.height - 4), "🔄 Parse"))
        {
            Undo.RecordObject(document, "Parse Full Text");
            document.ParseAndConfigureText(document.fullText);
            EditorUtility.SetDirty(document);
        }
        
        if (_showFullText)
        {
            EditorGUI.indentLevel++;
            
            SerializedProperty fullTextProp = serializedObject.FindProperty("fullText");
            
            // Word/character count
            string currentText = fullTextProp.stringValue ?? "";
            int wordCount = string.IsNullOrEmpty(currentText) ? 0 : 
                currentText.Split(' ', System.StringSplitOptions.RemoveEmptyEntries).Length;
            
            EditorGUILayout.LabelField($"Words: {wordCount} | Characters: {currentText.Length}", EditorStyles.miniLabel);
            
            // Text area
            fullTextProp.stringValue = EditorGUILayout.TextArea(fullTextProp.stringValue, GUILayout.Height(100));
            
            EditorGUI.indentLevel--;
        }
        
        GUILayout.Space(5);
    }
    
    private void DrawDefaultsSection()
    {
        Rect sectionRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(sectionRect, _headerColor);
        
        _showDefaults = EditorGUI.Foldout(new Rect(sectionRect.x + 5, sectionRect.y + 3, sectionRect.width, sectionRect.height), 
                                         _showDefaults, "⚙️ Default Settings", true, EditorStyles.foldoutHeader);
        
        if (_showDefaults)
        {
            EditorGUI.indentLevel++;
            
            EditorGUILayout.LabelField("Visual Defaults", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultTextColor"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultFontAsset"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultFontSize"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultIsBold"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultIsItalic"));
            
            GUILayout.Space(5);
            EditorGUILayout.LabelField("Timing Defaults", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultDelayAfterSentence"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultDelayAfterPhrase"));
            
            GUILayout.Space(5);
            EditorGUILayout.LabelField("Typewriter Defaults", EditorStyles.boldLabel);
            EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultUseTypewriter"));
            
            VisualTextDocument document = (VisualTextDocument)target;
            if (document.DefaultUseTypewriter)
            {
                EditorGUI.indentLevel++;
                EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultDelayPerCharacter"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultPauseOnPunctuation"));
                EditorGUILayout.PropertyField(serializedObject.FindProperty("DefaultPunctuationDelay"));
                EditorGUI.indentLevel--;
            }
            
            EditorGUI.indentLevel--;
        }
        
        GUILayout.Space(5);
    }
    
    private void DrawStatisticsSection(VisualTextDocument document)
    {
        Rect sectionRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(sectionRect, _warningColor);
        
        _showStatistics = EditorGUI.Foldout(new Rect(sectionRect.x + 5, sectionRect.y + 3, sectionRect.width, sectionRect.height), 
                                           _showStatistics, "📊 Statistics", true, EditorStyles.foldoutHeader);
        
        if (_showStatistics)
        {
            EditorGUI.indentLevel++;
            
            int totalSentences = document.Phrases?.Sum(p => p.Sentences?.Count ?? 0) ?? 0;
            int totalWords = document.Phrases?.Sum(p => p.Sentences?.Sum(s => 
                string.IsNullOrEmpty(s.Text) ? 0 : s.Text.Split(' ', System.StringSplitOptions.RemoveEmptyEntries).Length) ?? 0) ?? 0;
            int totalCharacters = document.Phrases?.Sum(p => p.Sentences?.Sum(s => s.Text?.Length ?? 0) ?? 0) ?? 0;
            
            EditorGUILayout.LabelField($"Phrases: {document.Phrases?.Count ?? 0}");
            EditorGUILayout.LabelField($"Sentences: {totalSentences}");
            EditorGUILayout.LabelField($"Words: {totalWords}");
            EditorGUILayout.LabelField($"Characters: {totalCharacters}");
            
            if (totalWords > 0)
            {
                EditorGUILayout.LabelField($"Estimated reading time: {totalWords / 200f:F1} minutes (200 WPM)");
            }
            
            EditorGUI.indentLevel--;
        }
        
        GUILayout.Space(5);
    }
    
    private void DrawPhrasesSection(VisualTextDocument document)
    {
        Rect sectionRect = EditorGUILayout.GetControlRect(false, 25);
        EditorGUI.DrawRect(sectionRect, _successColor);
        
        _showPhrases = EditorGUI.Foldout(new Rect(sectionRect.x + 5, sectionRect.y + 3, sectionRect.width - 100, sectionRect.height), 
                                        _showPhrases, $"📄 Phrases ({document.Phrases?.Count ?? 0})", true, EditorStyles.foldoutHeader);
        
        // Search field in header
        _searchFilter = EditorGUI.TextField(new Rect(sectionRect.xMax - 95, sectionRect.y + 2, 90, sectionRect.height - 4), 
                                           _searchFilter, EditorStyles.toolbarSearchField);
        
        if (_showPhrases)
        {
            if (document.Phrases == null || document.Phrases.Count == 0)
            {
                EditorGUILayout.HelpBox("No phrases found. Parse the full text to generate phrases.", MessageType.Info);
                return;
            }
            
            EditorGUI.indentLevel++;
            
            SerializedProperty phrasesProp = serializedObject.FindProperty("Phrases");
            
            for (int i = 0; i < document.Phrases.Count; i++)
            {
                var phrase = document.Phrases[i];
                
                // Apply search filter
                if (!string.IsNullOrEmpty(_searchFilter))
                {
                    bool matchFound = false;
                    if (phrase.FullPhraseText != null && phrase.FullPhraseText.ToLower().Contains(_searchFilter.ToLower()))
                        matchFound = true;
                    
                    if (phrase.Sentences != null)
                    {
                        foreach (var sentence in phrase.Sentences)
                        {
                            if (sentence.Text != null && sentence.Text.ToLower().Contains(_searchFilter.ToLower()))
                            {
                                matchFound = true;
                                break;
                            }
                        }
                    }
                    
                    if (!matchFound) continue;
                }
                
                SerializedProperty phraseProp = phrasesProp.GetArrayElementAtIndex(i);
                EditorGUILayout.PropertyField(phraseProp, new GUIContent($"Phrase {i + 1}"), true);
                
                GUILayout.Space(2);
            }
            
            EditorGUI.indentLevel--;
        }
        
        GUILayout.Space(5);
    }
    
    private void DrawFooter(VisualTextDocument document)
    {
        // Toggle buttons for sections
        GUILayout.BeginHorizontal();
        
        if (GUILayout.Button(_showFullText ? "📝" : "📝", GUILayout.Width(30)))
            _showFullText = !_showFullText;
            
        if (GUILayout.Button(_showDefaults ? "⚙️" : "⚙️", GUILayout.Width(30)))
            _showDefaults = !_showDefaults;
            
        if (GUILayout.Button(_showStatistics ? "📊" : "📊", GUILayout.Width(30)))
            _showStatistics = !_showStatistics;
            
        if (GUILayout.Button(_showPhrases ? "📄" : "📄", GUILayout.Width(30)))
            _showPhrases = !_showPhrases;
        
        GUILayout.FlexibleSpace();
        
        // Quick actions
        if (GUILayout.Button("🔄 Parse", GUILayout.Width(60)))
        {
            Undo.RecordObject(document, "Parse Full Text");
            document.ParseAndConfigureText(document.fullText);
            EditorUtility.SetDirty(document);
        }
        
        if (GUILayout.Button("🖼️ Editor", GUILayout.Width(60)))
        {
            VisualTextDocumentEditorWindow.ShowWindow(document);
        }
        
        GUILayout.EndHorizontal();
    }
}
