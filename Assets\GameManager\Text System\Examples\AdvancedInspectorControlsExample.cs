using UnityEngine;
using System.Collections.Generic;

/// <summary>
/// Exemplo demonstrando como usar os controles avançados do Inspector do Visual Text Displayer.
/// Este script mostra como integrar os controles do Inspector com lógica de jogo personalizada.
/// </summary>
public class AdvancedInspectorControlsExample : MonoBehaviour
{
    [Header("Visual Text Displayer Setup")]
    [Tooltip("O Visual Text Displayer que será controlado via Inspector.")]
    public VisualTextDisplayer textDisplayer;
    
    [Header("Document Collection")]
    [Tooltip("Lista de documentos para demonstrar navegação avançada.")]
    public List<VisualTextDocument> documentLibrary = new List<VisualTextDocument>();
    
    [Tooltip("Índice do documento atual na biblioteca.")]
    public int currentDocumentIndex = 0;
    
    [Header("Inspector Integration")]
    [Tooltip("Se deve sincronizar automaticamente com os controles do Inspector.")]
    public bool syncWithInspector = true;
    
    [Tooltip("Se deve registrar mudanças de estado no console.")]
    public bool logStateChanges = true;
    
    [Header("Auto-Navigation Demo")]
    [Tooltip("Se deve demonstrar navegação automática.")]
    public bool enableAutoNavigation = false;
    
    [Tooltip("Intervalo entre navegações automáticas (segundos).")]
    [Range(1f, 10f)]
    public float autoNavigationInterval = 3f;
    
    // Estado interno
    private int lastPhraseIndex = -1;
    private int lastSentenceIndex = -1;
    private bool lastPlayingState = false;
    private float lastAutoNavigationTime = 0f;

    void Start()
    {
        SetupExample();
    }

    void SetupExample()
    {
        if (textDisplayer == null)
        {
            textDisplayer = GetComponent<VisualTextDisplayer>();
            if (textDisplayer == null)
            {
                Debug.LogWarning("No Visual Text Displayer found! Please assign one in the Inspector.");
                return;
            }
        }

        // Carrega o primeiro documento se disponível
        if (documentLibrary.Count > 0 && currentDocumentIndex >= 0 && currentDocumentIndex < documentLibrary.Count)
        {
            LoadDocument(currentDocumentIndex);
        }

        if (logStateChanges)
        {
            Debug.Log("Advanced Inspector Controls Example initialized.");
            Debug.Log("Use the Inspector Controls tab to navigate and control text playback.");
            Debug.Log("This script will monitor and log state changes automatically.");
        }
    }

    void Update()
    {
        if (textDisplayer == null) return;

        // Monitora mudanças de estado se sincronização estiver ativa
        if (syncWithInspector)
        {
            MonitorStateChanges();
        }

        // Demonstração de navegação automática
        if (enableAutoNavigation && Application.isPlaying)
        {
            HandleAutoNavigation();
        }

        // Controles de teclado para demonstração
        HandleKeyboardControls();
    }

    void MonitorStateChanges()
    {
        if (!Application.isPlaying) return;

        // Monitora mudanças na posição de navegação
        int currentPhraseIndex = textDisplayer.GetCurrentPhraseIndex();
        int currentSentenceIndex = textDisplayer.GetCurrentSentenceIndex();
        bool currentPlayingState = textDisplayer.IsPlaying();

        if (currentPhraseIndex != lastPhraseIndex)
        {
            if (logStateChanges)
                Debug.Log($"Phrase changed: {lastPhraseIndex} → {currentPhraseIndex}");
            lastPhraseIndex = currentPhraseIndex;
            OnPhraseChanged(currentPhraseIndex);
        }

        if (currentSentenceIndex != lastSentenceIndex)
        {
            if (logStateChanges)
                Debug.Log($"Sentence changed: {lastSentenceIndex} → {currentSentenceIndex}");
            lastSentenceIndex = currentSentenceIndex;
            OnSentenceChanged(currentSentenceIndex);
        }

        if (currentPlayingState != lastPlayingState)
        {
            if (logStateChanges)
                Debug.Log($"Playing state changed: {lastPlayingState} → {currentPlayingState}");
            lastPlayingState = currentPlayingState;
            OnPlayingStateChanged(currentPlayingState);
        }
    }

    void HandleAutoNavigation()
    {
        if (Time.time - lastAutoNavigationTime >= autoNavigationInterval)
        {
            lastAutoNavigationTime = Time.time;
            
            // Navega automaticamente para a próxima sentença
            if (textDisplayer._currentDocument != null)
            {
                textDisplayer.NextSentence();
                
                if (logStateChanges)
                    Debug.Log("Auto-navigation: Advanced to next sentence");
            }
        }
    }

    void HandleKeyboardControls()
    {
        if (!Application.isPlaying) return;

        // Navegação por documentos
        if (Input.GetKeyDown(KeyCode.PageUp))
        {
            PreviousDocument();
        }
        else if (Input.GetKeyDown(KeyCode.PageDown))
        {
            NextDocument();
        }

        // Controles de demonstração
        if (Input.GetKeyDown(KeyCode.F1))
        {
            DemonstrateInspectorFeatures();
        }
    }

    /// <summary>
    /// Carrega um documento específico da biblioteca.
    /// </summary>
    public void LoadDocument(int index)
    {
        if (index >= 0 && index < documentLibrary.Count)
        {
            currentDocumentIndex = index;
            var document = documentLibrary[index];
            
            if (textDisplayer != null && document != null)
            {
                textDisplayer.Display(document);
                
                if (logStateChanges)
                    Debug.Log($"Loaded document: {document.name} (Index: {index})");
            }
        }
    }

    /// <summary>
    /// Navega para o documento anterior na biblioteca.
    /// </summary>
    public void PreviousDocument()
    {
        if (documentLibrary.Count > 0)
        {
            currentDocumentIndex = (currentDocumentIndex - 1 + documentLibrary.Count) % documentLibrary.Count;
            LoadDocument(currentDocumentIndex);
        }
    }

    /// <summary>
    /// Navega para o próximo documento na biblioteca.
    /// </summary>
    public void NextDocument()
    {
        if (documentLibrary.Count > 0)
        {
            currentDocumentIndex = (currentDocumentIndex + 1) % documentLibrary.Count;
            LoadDocument(currentDocumentIndex);
        }
    }

    /// <summary>
    /// Demonstra as funcionalidades do Inspector programaticamente.
    /// </summary>
    [ContextMenu("Demonstrate Inspector Features")]
    public void DemonstrateInspectorFeatures()
    {
        if (!Application.isPlaying)
        {
            Debug.Log("Enter Play Mode to demonstrate Inspector features.");
            return;
        }

        if (textDisplayer == null || textDisplayer._currentDocument == null)
        {
            Debug.Log("No document loaded. Load a document first using the Inspector Controls.");
            return;
        }

        Debug.Log("=== Inspector Features Demonstration ===");
        Debug.Log("1. Use the Controls tab to test simple text and documents");
        Debug.Log("2. Use the Advanced Navigation section to jump to specific positions");
        Debug.Log("3. Use the Status tab to monitor real-time state and progress");
        Debug.Log("4. Try the playback controls for Play/Pause/Stop/Clear");
        Debug.Log("5. Use navigation controls for Previous/Next Phrase/Sentence");
        Debug.Log("Current document: " + textDisplayer._currentDocument.name);
        Debug.Log("Current position: Phrase " + (textDisplayer.GetCurrentPhraseIndex() + 1) + 
                 ", Sentence " + (textDisplayer.GetCurrentSentenceIndex() + 1));
    }

    // Event handlers para mudanças de estado
    protected virtual void OnPhraseChanged(int newPhraseIndex)
    {
        // Override este método para reagir a mudanças de frase
        // Exemplo: mudar música de fundo, efeitos visuais, etc.
    }

    protected virtual void OnSentenceChanged(int newSentenceIndex)
    {
        // Override este método para reagir a mudanças de sentença
        // Exemplo: destacar texto, mostrar imagens, etc.
    }

    protected virtual void OnPlayingStateChanged(bool isPlaying)
    {
        // Override este método para reagir a mudanças no estado de reprodução
        // Exemplo: pausar animações, mostrar/esconder UI, etc.
    }

    void OnValidate()
    {
        // Garante que o índice do documento esteja dentro dos limites
        if (documentLibrary.Count > 0)
        {
            currentDocumentIndex = Mathf.Clamp(currentDocumentIndex, 0, documentLibrary.Count - 1);
        }
        else
        {
            currentDocumentIndex = 0;
        }
    }

    void OnGUI()
    {
        if (!Application.isPlaying || !syncWithInspector) return;

        // Exibe informações de estado na tela para demonstração
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Inspector Controls Integration", GUI.skin.box);
        
        if (textDisplayer != null)
        {
            GUILayout.Label($"Document: {textDisplayer._currentDocument?.name ?? "None"}");
            GUILayout.Label($"Phrase: {textDisplayer.GetCurrentPhraseIndex() + 1}");
            GUILayout.Label($"Sentence: {textDisplayer.GetCurrentSentenceIndex() + 1}");
            GUILayout.Label($"State: {(textDisplayer.IsPlaying() ? "Playing" : "Stopped")}");
            
            if (textDisplayer.IsFastForwardActive())
                GUILayout.Label("⚡ Fast Forward Active");
        }
        
        GUILayout.Label("Press F1 for feature demonstration");
        GUILayout.Label("PageUp/PageDown: Change documents");
        
        GUILayout.EndArea();
    }
}
