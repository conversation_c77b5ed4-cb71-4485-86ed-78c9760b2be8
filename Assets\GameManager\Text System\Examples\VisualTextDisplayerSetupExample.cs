using UnityEngine;
using TMPro;

/// <summary>
/// Exemplo de como configurar um Visual Text Displayer usando o novo editor melhorado.
/// Este script demonstra a configuração básica e pode ser usado como referência.
/// </summary>
public class VisualTextDisplayerSetupExample : MonoBehaviour
{
    [Header("Visual Text Displayer Setup Example")]
    [Tooltip("O Visual Text Displayer que será configurado automaticamente.")]
    public VisualTextDisplayer textDisplayer;
    
    [Header("Test Documents")]
    [Tooltip("Documento de teste para demonstração.")]
    public VisualTextDocument testDocument;
    
    [Header("Auto Setup Options")]
    [Tooltip("Se deve configurar automaticamente os componentes no Start.")]
    public bool autoSetupOnStart = true;
    
    [Tooltip("Se deve criar um AudioSource automaticamente se não existir.")]
    public bool createAudioSourceIfMissing = true;

    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupVisualTextDisplayer();
        }
    }

    /// <summary>
    /// Configura automaticamente o Visual Text Displayer com componentes necessários.
    /// </summary>
    [ContextMenu("Setup Visual Text Displayer")]
    public void SetupVisualTextDisplayer()
    {
        // Se não há um displayer atribuído, tenta encontrar um no GameObject
        if (textDisplayer == null)
        {
            textDisplayer = GetComponent<VisualTextDisplayer>();
            
            // Se ainda não há um, cria um novo
            if (textDisplayer == null)
            {
                textDisplayer = gameObject.AddComponent<VisualTextDisplayer>();
                Debug.Log("VisualTextDisplayer component added to " + gameObject.name);
            }
        }

        // Configura o TextMeshProUGUI
        if (textDisplayer.textComponent == null)
        {
            var textComponent = GetComponent<TextMeshProUGUI>();
            
            if (textComponent == null)
            {
                // Cria um novo TextMeshProUGUI se não existir
                textComponent = gameObject.AddComponent<TextMeshProUGUI>();
                
                // Configurações básicas
                textComponent.text = "Visual Text Displayer Ready!";
                textComponent.fontSize = 36f;
                textComponent.color = Color.white;
                textComponent.alignment = TextAlignmentOptions.Center;
                
                Debug.Log("TextMeshProUGUI component added to " + gameObject.name);
            }
            
            textDisplayer.textComponent = textComponent;
        }

        // Configura o AudioSource se necessário
        if (createAudioSourceIfMissing && textDisplayer.audioSource == null)
        {
            var audioSource = GetComponent<AudioSource>();
            
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.volume = 0.5f;
                
                Debug.Log("AudioSource component added to " + gameObject.name);
            }
            
            textDisplayer.audioSource = audioSource;
        }

        Debug.Log("Visual Text Displayer setup completed for " + gameObject.name);
    }

    /// <summary>
    /// Testa a exibição de texto simples.
    /// </summary>
    [ContextMenu("Test Simple Text")]
    public void TestSimpleText()
    {
        if (textDisplayer == null)
        {
            Debug.LogWarning("No Visual Text Displayer assigned!");
            return;
        }

        string testText = "This is a simple text test. The Visual Text Displayer should display this text with typewriter effect.";
        
        if (Application.isPlaying)
        {
            textDisplayer.Display(testText);
        }
        else
        {
            Debug.Log("Test Simple Text: " + testText);
            Debug.Log("Enter Play Mode to see the actual text display.");
        }
    }

    /// <summary>
    /// Testa a exibição de um documento.
    /// </summary>
    [ContextMenu("Test Document")]
    public void TestDocument()
    {
        if (textDisplayer == null)
        {
            Debug.LogWarning("No Visual Text Displayer assigned!");
            return;
        }

        if (testDocument == null)
        {
            Debug.LogWarning("No test document assigned!");
            return;
        }

        if (Application.isPlaying)
        {
            textDisplayer.Display(testDocument);
        }
        else
        {
            Debug.Log("Test Document: " + testDocument.name);
            Debug.Log("Enter Play Mode to see the actual document display.");
        }
    }

    /// <summary>
    /// Limpa o texto exibido.
    /// </summary>
    [ContextMenu("Clear Text")]
    public void ClearText()
    {
        if (textDisplayer == null)
        {
            Debug.LogWarning("No Visual Text Displayer assigned!");
            return;
        }

        if (Application.isPlaying)
        {
            textDisplayer.Clear();
        }
        else if (textDisplayer.textComponent != null)
        {
            textDisplayer.textComponent.text = "";
        }
    }

    /// <summary>
    /// Demonstra as configurações padrão recomendadas.
    /// </summary>
    [ContextMenu("Apply Recommended Settings")]
    public void ApplyRecommendedSettings()
    {
        if (textDisplayer == null)
        {
            Debug.LogWarning("No Visual Text Displayer assigned!");
            return;
        }

        // Configurações recomendadas para texto simples
        textDisplayer.defaultTextColor = Color.white;
        textDisplayer.defaultFontSize = 36f;
        textDisplayer.defaultIsBold = false;
        textDisplayer.defaultIsItalic = false;
        textDisplayer.defaultAlignment = TextAlignmentOptions.Left;
        
        // Configurações de timing
        textDisplayer.defaultDelayAfterSentence = 0.5f;
        textDisplayer.defaultDelayAfterPhrase = 1.0f;
        
        // Configurações de typewriter
        textDisplayer.defaultUseTypewriter = true;
        textDisplayer.defaultDelayPerCharacter = 0.05f;
        textDisplayer.defaultPauseOnPunctuation = true;
        textDisplayer.defaultPunctuationDelay = 0.2f;
        
        // Configurações de fast forward
        textDisplayer.fastForwardTypewriterMultiplier = 5f;
        textDisplayer.fastForwardAnimationMultiplier = 3f;

        Debug.Log("Recommended settings applied to Visual Text Displayer");
        
        #if UNITY_EDITOR
        UnityEditor.EditorUtility.SetDirty(textDisplayer);
        #endif
    }

    void Update()
    {
        // Controles de teste durante runtime
        if (Application.isPlaying && textDisplayer != null)
        {
            // Teclas de teste rápido
            if (Input.GetKeyDown(KeyCode.T))
            {
                TestSimpleText();
            }
            
            if (Input.GetKeyDown(KeyCode.D) && testDocument != null)
            {
                TestDocument();
            }
            
            if (Input.GetKeyDown(KeyCode.X))
            {
                ClearText();
            }
        }
    }

    void OnValidate()
    {
        // Validação automática no editor
        if (textDisplayer == null)
        {
            textDisplayer = GetComponent<VisualTextDisplayer>();
        }
    }
}
